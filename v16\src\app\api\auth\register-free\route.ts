// ===== Archivo: src/app/api/auth/register-free/route.ts (USANDO CLIENTE NORMAL) =====

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { supabaseAdmin } from '@/lib/supabase/admin';

export async function POST(request: NextRequest) {
  try {
    const { email, password, customerName } = await request.json();

    if (!email || !password) {
      return NextResponse.json({ error: 'Email y contraseña son requeridos' }, { status: 400 });
    }
    if (password.length < 6) {
      return NextResponse.json({ error: 'La contraseña debe tener al menos 6 caracteres' }, { status: 400 });
    }

    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 5);

    // Crear cliente normal (no admin) para que envíe emails automáticamente
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
    );

     ('🚀 Iniciando registro con cliente normal...');

    // Usar signUp del cliente normal - esto SÍ envía emails automáticamente
    const { data, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name: customerName || email.split('@')[0],
          plan: 'free',
          free_account: true,
          expires_at: expiresAt.toISOString(),
        },
        emailRedirectTo: `${process.env.NEXT_PUBLIC_APP_URL}/auth/confirmed`
      }
    });

     ('📧 Resultado de signUp:', {
      user: data.user?.id,
      session: !!data.session,
      error: signUpError
    });

    if (signUpError) {
      console.error('❌ Error en signUp:', signUpError);
      if (signUpError.message.includes('User already registered')) {
        return NextResponse.json({ error: 'Ya existe una cuenta con este email.' }, { status: 409 });
      }
      throw signUpError;
    }

    if (!data.user) {
      throw new Error("No se pudo crear el usuario.");
    }

     ('✅ Usuario creado con cliente normal:', data.user.id);
     ('📧 Email de confirmación enviado automáticamente');

    // Crear el perfil de usuario usando admin client
    await supabaseAdmin.from('user_profiles').insert({
      user_id: data.user.id,
      subscription_plan: 'free',
      plan_expires_at: expiresAt.toISOString(),
      payment_verified: true,
      monthly_token_limit: 50000,
      current_month_tokens: 0,
      security_flags: {
        created_via_signup: true,
        free_account: true,
        expires_at: expiresAt.toISOString()
      }
    });
     ('✅ Perfil de usuario creado para:', data.user.id);

    return NextResponse.json({
      success: true,
      message: '¡Registro exitoso! Revisa tu email para confirmar tu cuenta y poder iniciar sesión.',
    });

  } catch (error) {
    console.error('❌ Error en registro gratuito:', error);
    const errorMessage = error instanceof Error ? error.message : 'Error interno del servidor.';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}