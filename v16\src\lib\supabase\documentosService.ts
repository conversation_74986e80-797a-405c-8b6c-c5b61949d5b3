import { supabase, Documento } from './supabaseClient';
import { obtenerUsuarioActual } from './authService';

/**
 * Obtiene todos los documentos del usuario actual ordenados por número de tema
 */
export async function obtenerDocumentos(): Promise<Documento[]> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return [];
    }

    const { data, error } = await supabase
      .from('documentos')
      .select('*')
      .eq('user_id', user.id)
      .order('numero_tema', { ascending: true });

    if (error) {
      console.error('Error al obtener documentos:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error al obtener documentos:', error);
    return [];
  }
}

/**
 * Obtiene un documento específico por su ID (solo si pertenece al usuario actual)
 */
export async function obtenerDocumentoPorId(id: string): Promise<Documento | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    const { data, error } = await supabase
      .from('documentos')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      console.error('Error al obtener documento:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error al obtener documento:', error);
    return null;
  }
}

/**
 * Guarda un nuevo documento en la base de datos asociado al usuario actual
 */
export async function guardarDocumento(documento: Omit<Documento, 'id' | 'creado_en' | 'actualizado_en' | 'user_id'>): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('No hay usuario autenticado');
      return null;
    }

    // Añadir el user_id y tipo_original al documento
    const documentoConUsuario = {
      ...documento,
      user_id: user.id,
      tipo_original: documento.tipo_original // Asegurarse que tipo_original se pasa aquí
    };

    const { data, error } = await supabase
      .from('documentos')
      .insert([documentoConUsuario])
      .select();

    if (error) {
      console.error('Error al guardar documento:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error al guardar documento:', error);
    return null;
  }
}

/**
 * Elimina un documento específico del usuario actual
 */
export async function eliminarDocumento(documentoId: string): Promise<boolean> {
  try {
    // Obtener el usuario actual
    const { user } = await obtenerUsuarioActual();

    if (!user) {
      console.error('❌ No hay usuario autenticado para eliminar documento');
      return false;
    }
    const { error, count } = await supabase
      .from('documentos')
      .delete({ count: 'exact' })
      .eq('id', documentoId)
      .eq('user_id', user.id); // Asegurar que solo se eliminen documentos del usuario actual

    if (error) {
      console.error('❌ Error al eliminar documento de Supabase:', error);
      return false;
    }
    if (count === 0) {
      console.warn('⚠️ No se eliminó ningún documento. Posibles causas: documento no existe o no pertenece al usuario');
      return false;
    }

    return true;
  } catch (error) {
    console.error('💥 Error inesperado al eliminar documento:', error);
    return false;
  }
}
