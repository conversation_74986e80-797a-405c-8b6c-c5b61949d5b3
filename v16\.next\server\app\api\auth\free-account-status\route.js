/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/free-account-status/route";
exports.ids = ["app/api/auth/free-account-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_free_account_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/auth/free-account-status/route.ts */ \"(rsc)/./src/app/api/auth/free-account-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/free-account-status/route\",\n        pathname: \"/api/auth/free-account-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/free-account-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\api\\\\auth\\\\free-account-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_naata_Documents_augment_projects_OposI_v16_src_app_api_auth_free_account_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/auth/free-account-status/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/auth/free-account-status/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/freeAccountService */ \"(rsc)/./src/lib/services/freeAccountService.ts\");\n// src/app/api/auth/free-account-status/route.ts\n// Endpoint para verificar estado de cuentas gratuitas\n\n\n\nasync function GET(request) {\n    try {\n        console.log('🔍 Verificando estado de cuenta gratuita');\n        // 1. Crear cliente de Supabase para verificar autenticación\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.HxY5DjCZBX9Mb0o_2Mi8IVM8F8wNgdr7n0w2EkGW3M4\", {\n            cookies: {\n                getAll () {\n                    return request.cookies.getAll();\n                },\n                setAll () {\n                // No necesitamos setear cookies en este endpoint\n                }\n            }\n        });\n        // 2. Verificar autenticación\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Usuario no autenticado'\n            }, {\n                status: 401\n            });\n        }\n        console.log('👤 Usuario autenticado:', user.id);\n        // 3. Obtener estado de la cuenta gratuita\n        const status = await _lib_services_freeAccountService__WEBPACK_IMPORTED_MODULE_2__.FreeAccountService.getFreeAccountStatus(user.id);\n        if (!status) {\n            // Usuario no tiene una cuenta gratuita activa o no se encontró perfil de cuenta gratuita\n            console.log('📊 Usuario no tiene cuenta gratuita - devolviendo isFreeAccount: false');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                isFreeAccount: false,\n                status: null,\n                alerts: [],\n                usageWarnings: [],\n                recommendations: [],\n                upgradeUrl: '/upgrade-plan'\n            });\n        }\n        console.log('📊 Estado de cuenta gratuita obtenido:', {\n            isActive: status.isActive,\n            daysRemaining: status.daysRemaining,\n            expiresAt: status.expiresAt\n        });\n        // 4. Calcular información adicional\n        const now = new Date();\n        // Verificar que expiresAt no sea null\n        if (!status.expiresAt) {\n            console.error(\"❌ status.expiresAt es null, no se puede calcular la información adicional.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Error interno: Faltan datos de expiración de la cuenta gratuita.'\n            }, {\n                status: 500\n            });\n        }\n        const expiresAtDate = new Date(status.expiresAt);\n        const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms\n        const timeElapsed = now.getTime() - (expiresAtDate.getTime() - totalDuration);\n        const progressPercentage = Math.min(100, Math.max(0, timeElapsed / totalDuration * 100));\n        // 5. Determinar alertas y recomendaciones\n        const alerts = [];\n        const recommendations = [];\n        if (!status.isActive) {\n            alerts.push({\n                type: 'error',\n                message: 'Tu cuenta gratuita ha expirado',\n                action: 'upgrade'\n            });\n            recommendations.push('Actualiza a un plan premium para continuar usando OposiAI');\n        } else if (status.daysRemaining <= 1) {\n            alerts.push({\n                type: 'warning',\n                message: `Tu cuenta expira en ${status.hoursRemaining} horas`,\n                action: 'upgrade'\n            });\n            recommendations.push('Considera actualizar tu plan antes de que expire');\n        } else if (status.daysRemaining <= 2) {\n            alerts.push({\n                type: 'info',\n                message: `Tu cuenta expira en ${status.daysRemaining} días`,\n                action: 'reminder'\n            });\n        }\n        // Verificar límites de uso\n        const usageWarnings = [];\n        // Asegurar que usageCount y limits existen\n        if (status.usageCount && status.limits) {\n            Object.entries(status.usageCount).forEach(([feature, used])=>{\n                // Asegurar que 'used' es un número\n                const usedAmount = typeof used === 'number' ? used : 0;\n                // Asegurar que limit no es undefined\n                const limit = status.limits[feature];\n                const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero\n                const percentage = usedAmount / limitAmount * 100;\n                if (percentage >= 100) {\n                    usageWarnings.push({\n                        feature,\n                        message: `Has alcanzado el límite de ${feature} (${usedAmount}/${limitAmount})`,\n                        severity: 'error'\n                    });\n                } else if (percentage >= 80) {\n                    usageWarnings.push({\n                        feature,\n                        message: `Cerca del límite de ${feature} (${usedAmount}/${limitAmount})`,\n                        severity: 'warning'\n                    });\n                }\n            });\n        } else {\n            console.warn(\"⚠️ No se pudo generar usageWarnings porque status.usageCount o status.limits no están definidos.\");\n        }\n        if (usageWarnings.length > 0) {\n            recommendations.push('Considera actualizar tu plan para obtener más recursos');\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            isFreeAccount: true,\n            status: {\n                isActive: status.isActive,\n                expiresAt: status.expiresAt,\n                daysRemaining: status.daysRemaining,\n                hoursRemaining: status.hoursRemaining,\n                progressPercentage: Math.round(progressPercentage),\n                usageCount: status.usageCount || {},\n                limits: status.limits || {},\n                usagePercentages: status.usageCount && status.limits ? Object.entries(status.usageCount).reduce((acc, [key, value])=>{\n                    const limit = status.limits[key];\n                    // Asegurar que limit no es undefined y no es 0\n                    const limitAmount = typeof limit === 'number' && limit > 0 ? limit : 1; // Evitar división por cero\n                    const usedAmount = typeof value === 'number' ? value : 0;\n                    acc[key] = Math.round(usedAmount / limitAmount * 100);\n                    return acc;\n                }, {}) : {} // Enviar objeto vacío si no hay datos\n            },\n            alerts,\n            usageWarnings,\n            recommendations,\n            upgradeUrl: '/upgrade-plan'\n        });\n    } catch (error) {\n        console.error('❌ Error verificando estado de cuenta gratuita:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Error interno del servidor',\n            details:  true ? error instanceof Error ? error.message : 'Error desconocido' : 0\n        }, {\n            status: 500\n        });\n    }\n} // NOTA: El endpoint POST ha sido eliminado porque el incremento de uso\n // ahora se hace automáticamente en el backend (api/ai/route.ts y api/document/upload)\n // después de operaciones exitosas. Ya no es necesario que el frontend\n // haga llamadas POST para incrementar contadores.\n //\n // El endpoint GET sigue siendo necesario para obtener el estado de la cuenta gratuita.\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/auth/free-account-status/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/config/plans.ts":
/*!*****************************!*\
  !*** ./src/config/plans.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PLAN_CONFIGURATIONS: () => (/* binding */ PLAN_CONFIGURATIONS),\n/* harmony export */   canPerformAction: () => (/* binding */ canPerformAction),\n/* harmony export */   checkUserFeatureAccess: () => (/* binding */ checkUserFeatureAccess),\n/* harmony export */   getPlanConfiguration: () => (/* binding */ getPlanConfiguration),\n/* harmony export */   getTokenLimitForPlan: () => (/* binding */ getTokenLimitForPlan),\n/* harmony export */   getTrialLimit: () => (/* binding */ getTrialLimit),\n/* harmony export */   getWeeklyLimit: () => (/* binding */ getWeeklyLimit),\n/* harmony export */   hasFeatureAccess: () => (/* binding */ hasFeatureAccess),\n/* harmony export */   isUnlimited: () => (/* binding */ isUnlimited)\n/* harmony export */ });\n// src/config/plans.ts\n// Configuración centralizada de límites y características por plan\n// Configuración completa de planes con límites y características\nconst PLAN_CONFIGURATIONS = {\n    free: {\n        id: 'free',\n        name: 'Plan Gratis',\n        price: 0,\n        limits: {\n            documents: 1,\n            mindMapsForTrial: 2,\n            testsForTrial: 10,\n            flashcardsForTrial: 10,\n            tokensForTrial: 50000,\n            features: [\n                'document_upload',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'ai_tutor_chat',\n            'summary_a1_a2'\n        ]\n    },\n    usuario: {\n        id: 'usuario',\n        name: 'Plan Usuario',\n        price: 1000,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 500000,\n            features: [\n                'document_upload',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation'\n            ]\n        },\n        features: [\n            'document_upload',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation'\n        ],\n        restrictedFeatures: [\n            'study_planning',\n            'summary_a1_a2'\n        ]\n    },\n    pro: {\n        id: 'pro',\n        name: 'Plan Pro',\n        price: 1500,\n        limits: {\n            documents: -1,\n            mindMapsPerWeek: -1,\n            testsPerWeek: -1,\n            flashcardsPerWeek: -1,\n            monthlyTokens: 1000000,\n            features: [\n                'document_upload',\n                'study_planning',\n                'ai_tutor_chat',\n                'test_generation',\n                'flashcard_generation',\n                'mind_map_generation',\n                'summary_a1_a2'\n            ]\n        },\n        features: [\n            'document_upload',\n            'study_planning',\n            'ai_tutor_chat',\n            'test_generation',\n            'flashcard_generation',\n            'mind_map_generation',\n            'summary_a1_a2'\n        ],\n        restrictedFeatures: []\n    }\n};\n// Funciones de utilidad\nfunction getPlanConfiguration(planId) {\n    return PLAN_CONFIGURATIONS[planId] || null;\n}\nfunction getTokenLimitForPlan(planId) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 50000;\n    // Para plan gratuito, usar tokensForTrial\n    if (planId === 'free') {\n        return config.limits.tokensForTrial || 50000;\n    }\n    // Para planes de pago, usar monthlyTokens\n    return config.limits.monthlyTokens || 500000;\n}\nfunction hasFeatureAccess(planId, feature) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return false;\n    // Si la característica está en la lista de restringidas, no tiene acceso\n    if (config.restrictedFeatures.includes(feature)) {\n        return false;\n    }\n    // Si no está restringida, verificar si está en las características permitidas\n    return config.features.includes(feature);\n}\nfunction getWeeklyLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) return 0;\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsPerWeek || 0;\n        case 'tests':\n            return config.limits.testsPerWeek || 0;\n        case 'flashcards':\n            return config.limits.flashcardsPerWeek || 0;\n        default:\n            return 0;\n    }\n}\n// Nueva función para obtener límites de trial (para plan gratuito)\nfunction getTrialLimit(planId, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config || planId !== 'free') return -1; // -1 para ilimitado o no aplica\n    switch(limitType){\n        case 'mindMaps':\n            return config.limits.mindMapsForTrial || 0;\n        case 'tests':\n            return config.limits.testsForTrial || 0;\n        case 'flashcards':\n            return config.limits.flashcardsForTrial || 0;\n        case 'tokens':\n            return config.limits.tokensForTrial || 0;\n        default:\n            return 0;\n    }\n}\nfunction isUnlimited(limit) {\n    return limit === -1;\n}\n// Validar si un usuario puede realizar una acción específica\nfunction canPerformAction(planId, feature, currentUsage, limitType) {\n    const config = getPlanConfiguration(planId);\n    if (!config) {\n        return {\n            allowed: false,\n            reason: 'Plan no válido'\n        };\n    }\n    // Verificar si tiene acceso a la característica\n    if (!hasFeatureAccess(planId, feature)) {\n        return {\n            allowed: false,\n            reason: `Característica ${feature} no disponible en ${config.name}`\n        };\n    }\n    // Verificar límites semanales si aplica\n    if (limitType) {\n        const weeklyLimit = getWeeklyLimit(planId, limitType);\n        if (!isUnlimited(weeklyLimit) && currentUsage >= weeklyLimit) {\n            return {\n                allowed: false,\n                reason: `Límite semanal de ${limitType} alcanzado (${weeklyLimit})`\n            };\n        }\n    }\n    return {\n        allowed: true\n    };\n}\n// Función para verificar acceso de usuario (para uso en frontend)\nasync function checkUserFeatureAccess(feature) {\n    try {\n        // Obtener el plan del usuario desde la API\n        const response = await fetch('/api/user/plan');\n        if (!response.ok) {\n            console.error('Error obteniendo plan del usuario');\n            // Si hay error, asumir plan gratuito\n            return hasFeatureAccess('free', feature);\n        }\n        const { plan } = await response.json();\n        const userPlan = plan || 'free';\n        // Usar la misma lógica que la función hasFeatureAccess\n        return hasFeatureAccess(userPlan, feature);\n    } catch (error) {\n        console.error('Error verificando acceso a característica:', error);\n        // En caso de error, asumir plan gratuito\n        return hasFeatureAccess('free', feature);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/config/plans.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/freeAccountService.ts":
/*!************************************************!*\
  !*** ./src/lib/services/freeAccountService.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FreeAccountService: () => (/* binding */ FreeAccountService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase/admin */ \"(rsc)/./src/lib/supabase/admin.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/config/plans */ \"(rsc)/./src/config/plans.ts\");\n// src/lib/services/freeAccountService.ts\n// Servicio para gestión automatizada de cuentas gratuitas\n\n\nclass FreeAccountService {\n    /**\n   * Crear cuenta gratuita automáticamente\n   * Ahora crea el usuario directamente y genera un enlace de tipo 'recovery' para establecer la contraseña.\n   */ static async createFreeAccount(request) {\n        try {\n            console.log('🆓 Iniciando creación de cuenta gratuita (flujo de invitación):', request.email);\n            // 1. Validar que el email no esté ya registrado\n            try {\n                const existingUser = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserByEmail(request.email);\n                if (existingUser) {\n                    return {\n                        success: false,\n                        error: 'El email ya está registrado en el sistema.'\n                    };\n                }\n            } catch (error) {\n                if (error instanceof Error && error.message.toLowerCase().includes('user not found')) {\n                    console.log('Usuario no existe en Auth, continuando con la invitación.');\n                } else {\n                    console.warn('Advertencia al verificar usuario existente, se continuará con el intento de invitación:', error);\n                }\n            }\n            // 2. Calcular fecha de expiración (5 días desde ahora)\n            const expiresAt = new Date();\n            expiresAt.setDate(expiresAt.getDate() + 5);\n            // 3. Preparar metadatos para el usuario invitado\n            const userDataForCreation = {\n                name: request.name || request.email.split('@')[0],\n                plan: 'free',\n                free_account: true,\n                expires_at: expiresAt.toISOString(),\n                created_via: 'free_invitation_flow',\n                registration_type: 'automatic_free_invitation',\n                requires_password_setup: true\n            };\n            console.log('📧 Invitando nuevo usuario con datos:', {\n                email: request.email,\n                userData: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            // 4. Invitar al usuario. Esto crea el usuario y envía el email de invitación.\n            const { data: { user: newUser }, error: inviteError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.inviteUserByEmail(request.email, {\n                data: userDataForCreation,\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirm-invitation`\n            });\n            if (inviteError) {\n                console.error('❌ Error invitando al usuario:', inviteError);\n                if (inviteError.message.includes('User already registered')) {\n                    return {\n                        success: false,\n                        error: 'Ya existe una cuenta con este email.'\n                    };\n                }\n                throw new Error(`Error invitando al usuario: ${inviteError.message}`);\n            }\n            if (!newUser) {\n                throw new Error('Usuario no devuelto después de la invitación.');\n            }\n            console.log('✅ Usuario invitado exitosamente a Supabase Auth:', newUser.id);\n            // 5. Crear perfil de usuario y registrar historial de forma atómica\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            if (!planConfig) {\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error('Configuración de plan gratuito no encontrada');\n            }\n            const currentMonth = new Date().toISOString().slice(0, 7) + '-01';\n            const profileDataForRPC = {\n                subscription_plan: 'free',\n                monthly_token_limit: (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getTokenLimitForPlan)('free'),\n                current_month_tokens: 0,\n                current_month: currentMonth,\n                payment_verified: true,\n                stripe_customer_id: null,\n                stripe_subscription_id: null,\n                last_payment_date: null,\n                auto_renew: false,\n                plan_expires_at: expiresAt.toISOString(),\n                plan_features: planConfig.features,\n                security_flags: {\n                    created_via_free_invitation_flow: true,\n                    free_account: true,\n                    expires_at: expiresAt.toISOString(),\n                    activation_date: new Date().toISOString(),\n                    usage_count: {\n                        documents: 0,\n                        tests: 0,\n                        flashcards: 0,\n                        mindMaps: 0,\n                        tokens: 0\n                    }\n                }\n            };\n            const { data: creationResult, error: rpcError } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc('create_user_profile_and_history', {\n                p_user_id: newUser.id,\n                p_transaction_id: null,\n                p_profile_data: profileDataForRPC\n            }).single();\n            if (rpcError) {\n                console.error('❌ Error al ejecutar la función RPC create_user_profile_and_history:', rpcError);\n                await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(newUser.id); // Cleanup\n                throw new Error(`Error en la creación atómica del perfil: ${rpcError.message}`);\n            }\n            const profileId = creationResult.created_profile_id;\n            console.log('✅ Perfil gratuito y historial creados atómicamente. Profile ID:', profileId);\n            console.log('🎉 Cuenta gratuita creada exitosamente con flujo de invitación.');\n            return {\n                success: true,\n                userId: newUser.id,\n                profileId: profileId,\n                expiresAt: expiresAt.toISOString()\n            };\n        } catch (error) {\n            console.error('❌ Error crítico en la creación de cuenta gratuita:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido al crear cuenta gratuita'\n            };\n        }\n    }\n    // Las funciones getFreeAccountStatus, incrementUsageCount, canPerformAction, cleanupExpiredAccounts\n    // permanecen igual que antes, ya que su lógica no depende directamente de cómo se creó el usuario,\n    // sino de los datos en user_profiles.\n    /**\n   * Verificar estado de cuenta gratuita\n   */ static async getFreeAccountStatus(userId) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return null;\n            }\n            const now = new Date();\n            const expiresAt = profile.plan_expires_at ? new Date(profile.plan_expires_at) : null;\n            if (!expiresAt) {\n                return null;\n            }\n            const isActive = now < expiresAt;\n            const timeDiff = expiresAt.getTime() - now.getTime();\n            const daysRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60 * 24)));\n            const hoursRemaining = Math.max(0, Math.ceil(timeDiff / (1000 * 60 * 60)));\n            const usageCount = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            usageCount.tokens = profile.current_month_tokens || 0;\n            const planConfig = (0,_config_plans__WEBPACK_IMPORTED_MODULE_1__.getPlanConfiguration)('free');\n            const limits = {\n                documents: planConfig?.limits.documents || 1,\n                tests: planConfig?.limits.testsForTrial || 10,\n                flashcards: planConfig?.limits.flashcardsForTrial || 10,\n                mindMaps: planConfig?.limits.mindMapsForTrial || 2,\n                tokens: planConfig?.limits.tokensForTrial || 50000\n            };\n            // Calcular progressPercentage\n            const totalDuration = 5 * 24 * 60 * 60 * 1000; // 5 días en ms\n            const creationDate = new Date(profile.created_at || Date.now()); // Usar created_at o now si no está\n            const activationDate = profile.security_flags?.activation_date ? new Date(profile.security_flags.activation_date) : creationDate;\n            const timeElapsed = now.getTime() - activationDate.getTime();\n            const progressPercentage = Math.min(100, Math.max(0, timeElapsed / totalDuration * 100));\n            return {\n                isActive,\n                expiresAt: expiresAt.toISOString(),\n                daysRemaining,\n                hoursRemaining,\n                usageCount,\n                limits,\n                // @ts-ignore Asegurar que progressPercentage está en el tipo si es necesario\n                progressPercentage: Math.round(progressPercentage)\n            };\n        } catch (error) {\n            console.error('Error obteniendo estado de cuenta gratuita:', error);\n            return null;\n        }\n    }\n    /**\n   * Incrementar contador de uso\n   */ static async incrementUsageCount(userId, feature, amount = 1) {\n        try {\n            const profile = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.SupabaseAdminService.getUserProfile(userId);\n            if (!profile || profile.subscription_plan !== 'free') {\n                return false;\n            }\n            const currentUsage = profile.security_flags?.usage_count || {\n                documents: 0,\n                tests: 0,\n                flashcards: 0,\n                mindMaps: 0,\n                tokens: 0\n            };\n            currentUsage[feature] = (currentUsage[feature] || 0) + amount;\n            const updateData = {\n                security_flags: {\n                    ...profile.security_flags,\n                    usage_count: currentUsage\n                },\n                updated_at: new Date().toISOString()\n            };\n            await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update(updateData).eq('user_id', userId);\n            return true;\n        } catch (error) {\n            console.error('Error incrementando contador de uso:', error);\n            return false;\n        }\n    }\n    /**\n   * Verificar si se puede realizar una acción\n   */ static async canPerformAction(userId, feature, amount = 1) {\n        try {\n            const status = await this.getFreeAccountStatus(userId);\n            if (!status) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta no encontrada o no es gratuita'\n                };\n            }\n            if (!status.isActive) {\n                return {\n                    allowed: false,\n                    reason: 'Cuenta gratuita expirada'\n                };\n            }\n            const currentUsage = status.usageCount[feature] || 0;\n            const limit = status.limits[feature];\n            const remaining = limit - currentUsage;\n            if (currentUsage + amount > limit) {\n                return {\n                    allowed: false,\n                    reason: `Límite de ${feature} alcanzado (${currentUsage}/${limit})`,\n                    remaining: Math.max(0, remaining)\n                };\n            }\n            return {\n                allowed: true,\n                remaining: remaining - amount\n            };\n        } catch (error) {\n            console.error('Error verificando acción:', error);\n            return {\n                allowed: false,\n                reason: 'Error interno'\n            };\n        }\n    }\n    /**\n   * Limpiar cuentas gratuitas expiradas\n   */ static async cleanupExpiredAccounts() {\n        try {\n            console.log('🧹 Iniciando limpieza de cuentas gratuitas expiradas');\n            const now = new Date().toISOString();\n            const { data: expiredProfiles, error } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('user_id, id').eq('subscription_plan', 'free').lt('plan_expires_at', now)// Añadir condición para asegurar que no se desactiven cuentas ya deshabilitadas\n            .neq('security_flags ->> account_disabled', 'true');\n            if (error) {\n                throw new Error(`Error buscando cuentas expiradas: ${error.message}`);\n            }\n            if (!expiredProfiles || expiredProfiles.length === 0) {\n                console.log('✅ No hay cuentas expiradas para limpiar');\n                return {\n                    cleaned: 0,\n                    errors: []\n                };\n            }\n            console.log(`🗑️ Encontradas ${expiredProfiles.length} cuentas expiradas para procesar`);\n            const errors = [];\n            let cleaned = 0;\n            for (const profile of expiredProfiles){\n                try {\n                    // Desactivar usuario en auth\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.updateUserById(profile.user_id, {\n                        user_metadata: {\n                            account_disabled: true,\n                            disabled_reason: 'free_account_expired'\n                        }\n                    });\n                    // Marcar perfil como inactivo\n                    // Obtener security_flags existentes para no sobrescribirlas\n                    const { data: currentProfileData } = await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').select('security_flags').eq('user_id', profile.user_id).single();\n                    const existingFlags = currentProfileData?.security_flags || {};\n                    await _lib_supabase_admin__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from('user_profiles').update({\n                        payment_verified: false,\n                        security_flags: {\n                            ...existingFlags,\n                            account_disabled: true,\n                            disabled_at: new Date().toISOString(),\n                            disabled_reason: 'free_account_expired'\n                        }\n                    }).eq('user_id', profile.user_id);\n                    cleaned++;\n                } catch (cleanupError) {\n                    const errorMsg = `Error limpiando usuario ${profile.user_id}: ${cleanupError instanceof Error ? cleanupError.message : String(cleanupError)}`;\n                    console.error(errorMsg);\n                    errors.push(errorMsg);\n                }\n            }\n            console.log(`✅ Limpieza completada: ${cleaned} cuentas procesadas, ${errors.length} errores`);\n            return {\n                cleaned,\n                errors\n            };\n        } catch (error) {\n            console.error('❌ Error en limpieza de cuentas:', error);\n            return {\n                cleaned: 0,\n                errors: [\n                    error instanceof Error ? error.message : 'Error desconocido'\n                ]\n            };\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3NlcnZpY2VzL2ZyZWVBY2NvdW50U2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSx5Q0FBeUM7QUFDekMsMERBQTBEO0FBRXNDO0FBQ3BCO0FBc0NyRSxNQUFNSTtJQUVYOzs7R0FHQyxHQUNELGFBQWFDLGtCQUFrQkMsT0FBaUMsRUFBOEI7UUFDNUYsSUFBSTtZQUNGQyxRQUFRQyxHQUFHLENBQUMsbUVBQW1FRixRQUFRRyxLQUFLO1lBRTVGLGdEQUFnRDtZQUNoRCxJQUFJO2dCQUNGLE1BQU1DLGVBQWUsTUFBTVYscUVBQW9CQSxDQUFDVyxjQUFjLENBQUNMLFFBQVFHLEtBQUs7Z0JBQzVFLElBQUlDLGNBQWM7b0JBQ2YsT0FBTzt3QkFBRUUsU0FBUzt3QkFBT0MsT0FBTztvQkFBNkM7Z0JBQ2hGO1lBQ0YsRUFBRSxPQUFPQSxPQUFPO2dCQUNkLElBQUlBLGlCQUFpQkMsU0FBU0QsTUFBTUUsT0FBTyxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQyxtQkFBbUI7b0JBQ3BGVixRQUFRQyxHQUFHLENBQUM7Z0JBQ2QsT0FBTztvQkFDTEQsUUFBUVcsSUFBSSxDQUFDLDJGQUEyRkw7Z0JBQzFHO1lBQ0Y7WUFFQSx1REFBdUQ7WUFDdkQsTUFBTU0sWUFBWSxJQUFJQztZQUN0QkQsVUFBVUUsT0FBTyxDQUFDRixVQUFVRyxPQUFPLEtBQUs7WUFFeEMsaURBQWlEO1lBQ2pELE1BQU1DLHNCQUFzQjtnQkFDMUJDLE1BQU1sQixRQUFRa0IsSUFBSSxJQUFJbEIsUUFBUUcsS0FBSyxDQUFDZ0IsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO2dCQUNqREMsTUFBTTtnQkFDTkMsY0FBYztnQkFDZEMsWUFBWVQsVUFBVVUsV0FBVztnQkFDakNDLGFBQWE7Z0JBQ2JDLG1CQUFtQjtnQkFDbkJDLHlCQUF5QjtZQUMzQjtZQUVBekIsUUFBUUMsR0FBRyxDQUFDLHlDQUF5QztnQkFDbkRDLE9BQU9ILFFBQVFHLEtBQUs7Z0JBQ3BCd0IsVUFBVVY7Z0JBQ1ZXLFlBQVksR0FBR0MsdUJBQStCLENBQUMsd0JBQXdCLENBQUM7WUFDMUU7WUFFQSw4RUFBOEU7WUFDOUUsTUFBTSxFQUFFRyxNQUFNLEVBQUVDLE1BQU1DLE9BQU8sRUFBRSxFQUFFM0IsT0FBTzRCLFdBQVcsRUFBRSxHQUFHLE1BQU14Qyw4REFBYUEsQ0FBQ3lDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxpQkFBaUIsQ0FDdEd0QyxRQUFRRyxLQUFLLEVBQ2I7Z0JBQ0U2QixNQUFNZjtnQkFDTlcsWUFBWSxHQUFHQyx1QkFBK0IsQ0FBQyx3QkFBd0IsQ0FBQztZQUMxRTtZQUdGLElBQUlNLGFBQWE7Z0JBQ2ZsQyxRQUFRTSxLQUFLLENBQUMsaUNBQWlDNEI7Z0JBQy9DLElBQUlBLFlBQVkxQixPQUFPLENBQUNFLFFBQVEsQ0FBQyw0QkFBNEI7b0JBQ3pELE9BQU87d0JBQUVMLFNBQVM7d0JBQU9DLE9BQU87b0JBQXVDO2dCQUMzRTtnQkFDQSxNQUFNLElBQUlDLE1BQU0sQ0FBQyw0QkFBNEIsRUFBRTJCLFlBQVkxQixPQUFPLEVBQUU7WUFDdEU7WUFDQSxJQUFJLENBQUN5QixTQUFTO2dCQUNaLE1BQU0sSUFBSTFCLE1BQU07WUFDbEI7WUFFQVAsUUFBUUMsR0FBRyxDQUFDLG9EQUFvRGdDLFFBQVFLLEVBQUU7WUFFMUUsb0VBQW9FO1lBQ3BFLE1BQU1DLGFBQWE1QyxtRUFBb0JBLENBQUM7WUFDeEMsSUFBSSxDQUFDNEMsWUFBWTtnQkFDZixNQUFNN0MsOERBQWFBLENBQUN5QyxJQUFJLENBQUNDLEtBQUssQ0FBQ0ksVUFBVSxDQUFDUCxRQUFRSyxFQUFFLEdBQUcsVUFBVTtnQkFDakUsTUFBTSxJQUFJL0IsTUFBTTtZQUNsQjtZQUVBLE1BQU1rQyxlQUFlLElBQUk1QixPQUFPUyxXQUFXLEdBQUdvQixLQUFLLENBQUMsR0FBRyxLQUFLO1lBQzVELE1BQU1DLG9CQUFvQjtnQkFDeEJDLG1CQUFtQjtnQkFDbkJDLHFCQUFxQmpELG1FQUFvQkEsQ0FBQztnQkFDMUNrRCxzQkFBc0I7Z0JBQ3RCQyxlQUFlTjtnQkFDZk8sa0JBQWtCO2dCQUNsQkMsb0JBQW9CO2dCQUNwQkMsd0JBQXdCO2dCQUN4QkMsbUJBQW1CO2dCQUNuQkMsWUFBWTtnQkFDWkMsaUJBQWlCekMsVUFBVVUsV0FBVztnQkFDdENnQyxlQUFlZixXQUFXZ0IsUUFBUTtnQkFDbENDLGdCQUFnQjtvQkFDZEMsa0NBQWtDO29CQUNsQ3JDLGNBQWM7b0JBQ2RDLFlBQVlULFVBQVVVLFdBQVc7b0JBQ2pDb0MsaUJBQWlCLElBQUk3QyxPQUFPUyxXQUFXO29CQUN2Q3FDLGFBQWE7d0JBQUVDLFdBQVc7d0JBQUdDLE9BQU87d0JBQUdDLFlBQVk7d0JBQUdDLFVBQVU7d0JBQUdDLFFBQVE7b0JBQUU7Z0JBQy9FO1lBQ0Y7WUFFQSxNQUFNLEVBQUVqQyxNQUFNa0MsY0FBYyxFQUFFM0QsT0FBTzRELFFBQVEsRUFBRSxHQUFHLE1BQU14RSw4REFBYUEsQ0FDbEV5RSxHQUFHLENBQUMsbUNBQW1DO2dCQUN0Q0MsV0FBV25DLFFBQVFLLEVBQUU7Z0JBQ3JCK0Isa0JBQWtCO2dCQUNsQkMsZ0JBQWdCM0I7WUFDbEIsR0FDQzRCLE1BQU07WUFFVCxJQUFJTCxVQUFVO2dCQUNabEUsUUFBUU0sS0FBSyxDQUFDLHVFQUF1RTREO2dCQUNyRixNQUFNeEUsOERBQWFBLENBQUN5QyxJQUFJLENBQUNDLEtBQUssQ0FBQ0ksVUFBVSxDQUFDUCxRQUFRSyxFQUFFLEdBQUcsVUFBVTtnQkFDakUsTUFBTSxJQUFJL0IsTUFBTSxDQUFDLHlDQUF5QyxFQUFFMkQsU0FBUzFELE9BQU8sRUFBRTtZQUNoRjtZQUVBLE1BQU1nRSxZQUFZLGVBQXdCQyxrQkFBa0I7WUFDNUR6RSxRQUFRQyxHQUFHLENBQUMsbUVBQW1FdUU7WUFFL0V4RSxRQUFRQyxHQUFHLENBQUM7WUFFWixPQUFPO2dCQUNMSSxTQUFTO2dCQUNUcUUsUUFBUXpDLFFBQVFLLEVBQUU7Z0JBQ2xCa0MsV0FBV0E7Z0JBQ1g1RCxXQUFXQSxVQUFVVSxXQUFXO1lBQ2xDO1FBRUYsRUFBRSxPQUFPaEIsT0FBTztZQUNkTixRQUFRTSxLQUFLLENBQUMsc0RBQXNEQTtZQUNwRSxPQUFPO2dCQUNMRCxTQUFTO2dCQUNUQyxPQUFPQSxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBRztZQUNsRDtRQUNGO0lBQ0Y7SUFFQSxvR0FBb0c7SUFDcEcsbUdBQW1HO0lBQ25HLHNDQUFzQztJQUV0Qzs7R0FFQyxHQUNELGFBQWFtRSxxQkFBcUJELE1BQWMsRUFBcUM7UUFDbkYsSUFBSTtZQUNGLE1BQU1FLFVBQVUsTUFBTW5GLHFFQUFvQkEsQ0FBQ29GLGNBQWMsQ0FBQ0g7WUFFMUQsSUFBSSxDQUFDRSxXQUFXQSxRQUFRaEMsaUJBQWlCLEtBQUssUUFBUTtnQkFDcEQsT0FBTztZQUNUO1lBRUEsTUFBTWtDLE1BQU0sSUFBSWpFO1lBQ2hCLE1BQU1ELFlBQVlnRSxRQUFRdkIsZUFBZSxHQUFHLElBQUl4QyxLQUFLK0QsUUFBUXZCLGVBQWUsSUFBSTtZQUVoRixJQUFJLENBQUN6QyxXQUFXO2dCQUNkLE9BQU87WUFDVDtZQUVBLE1BQU1tRSxXQUFXRCxNQUFNbEU7WUFDdkIsTUFBTW9FLFdBQVdwRSxVQUFVcUUsT0FBTyxLQUFLSCxJQUFJRyxPQUFPO1lBQ2xELE1BQU1DLGdCQUFnQkMsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLElBQUksQ0FBQ0wsV0FBWSxRQUFPLEtBQUssS0FBSyxFQUFDO1lBQzFFLE1BQU1NLGlCQUFpQkgsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtFLElBQUksQ0FBQ0wsV0FBWSxRQUFPLEtBQUssRUFBQztZQUV0RSxNQUFNTyxhQUFhWCxRQUFRcEIsY0FBYyxFQUFFRyxlQUFlO2dCQUN4REMsV0FBVztnQkFBR0MsT0FBTztnQkFBR0MsWUFBWTtnQkFBR0MsVUFBVTtnQkFBR0MsUUFBUTtZQUM5RDtZQUNBdUIsV0FBV3ZCLE1BQU0sR0FBR1ksUUFBUTlCLG9CQUFvQixJQUFJO1lBRXBELE1BQU1QLGFBQWE1QyxtRUFBb0JBLENBQUM7WUFDeEMsTUFBTTZGLFNBQVM7Z0JBQ2I1QixXQUFXckIsWUFBWWlELE9BQU81QixhQUFhO2dCQUMzQ0MsT0FBT3RCLFlBQVlpRCxPQUFPQyxpQkFBaUI7Z0JBQzNDM0IsWUFBWXZCLFlBQVlpRCxPQUFPRSxzQkFBc0I7Z0JBQ3JEM0IsVUFBVXhCLFlBQVlpRCxPQUFPRyxvQkFBb0I7Z0JBQ2pEM0IsUUFBUXpCLFlBQVlpRCxPQUFPSSxrQkFBa0I7WUFDL0M7WUFFQSw4QkFBOEI7WUFDOUIsTUFBTUMsZ0JBQWdCLElBQUksS0FBSyxLQUFLLEtBQUssTUFBTSxlQUFlO1lBQzlELE1BQU1DLGVBQWUsSUFBSWpGLEtBQUsrRCxRQUFRbUIsVUFBVSxJQUFJbEYsS0FBS2lFLEdBQUcsS0FBSyxtQ0FBbUM7WUFDcEcsTUFBTWtCLGlCQUFpQnBCLFFBQVFwQixjQUFjLEVBQUVFLGtCQUFrQixJQUFJN0MsS0FBSytELFFBQVFwQixjQUFjLENBQUNFLGVBQWUsSUFBSW9DO1lBQ3BILE1BQU1HLGNBQWNuQixJQUFJRyxPQUFPLEtBQUtlLGVBQWVmLE9BQU87WUFDMUQsTUFBTWlCLHFCQUFxQmYsS0FBS2dCLEdBQUcsQ0FBQyxLQUFLaEIsS0FBS0MsR0FBRyxDQUFDLEdBQUcsY0FBZVMsZ0JBQWlCO1lBR3JGLE9BQU87Z0JBQ0xkO2dCQUNBbkUsV0FBV0EsVUFBVVUsV0FBVztnQkFDaEM0RDtnQkFDQUk7Z0JBQ0FDO2dCQUNBQztnQkFDQSw2RUFBNkU7Z0JBQzdFVSxvQkFBb0JmLEtBQUtpQixLQUFLLENBQUNGO1lBQ2pDO1FBRUYsRUFBRSxPQUFPNUYsT0FBTztZQUNkTixRQUFRTSxLQUFLLENBQUMsK0NBQStDQTtZQUM3RCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYStGLG9CQUNYM0IsTUFBYyxFQUNkNEIsT0FBMEQsRUFDMURDLFNBQWlCLENBQUMsRUFDQTtRQUNsQixJQUFJO1lBQ0YsTUFBTTNCLFVBQVUsTUFBTW5GLHFFQUFvQkEsQ0FBQ29GLGNBQWMsQ0FBQ0g7WUFFMUQsSUFBSSxDQUFDRSxXQUFXQSxRQUFRaEMsaUJBQWlCLEtBQUssUUFBUTtnQkFDcEQsT0FBTztZQUNUO1lBRUEsTUFBTTRELGVBQWU1QixRQUFRcEIsY0FBYyxFQUFFRyxlQUFlO2dCQUMxREMsV0FBVztnQkFBR0MsT0FBTztnQkFBR0MsWUFBWTtnQkFBR0MsVUFBVTtnQkFBR0MsUUFBUTtZQUM5RDtZQUVBd0MsWUFBWSxDQUFDRixRQUFRLEdBQUcsQ0FBQ0UsWUFBWSxDQUFDRixRQUFRLElBQUksS0FBS0M7WUFFdkQsTUFBTUUsYUFBMkM7Z0JBQy9DakQsZ0JBQWdCO29CQUNkLEdBQUdvQixRQUFRcEIsY0FBYztvQkFDekJHLGFBQWE2QztnQkFDZjtnQkFDQUUsWUFBWSxJQUFJN0YsT0FBT1MsV0FBVztZQUNwQztZQUVBLE1BQU01Qiw4REFBYUEsQ0FDaEJpSCxJQUFJLENBQUMsaUJBQ0xDLE1BQU0sQ0FBQ0gsWUFDUEksRUFBRSxDQUFDLFdBQVduQztZQUVqQixPQUFPO1FBRVQsRUFBRSxPQUFPcEUsT0FBTztZQUNkTixRQUFRTSxLQUFLLENBQUMsd0NBQXdDQTtZQUN0RCxPQUFPO1FBQ1Q7SUFDRjtJQUVBOztHQUVDLEdBQ0QsYUFBYXdHLGlCQUNYcEMsTUFBYyxFQUNkNEIsT0FBcUUsRUFDckVDLFNBQWlCLENBQUMsRUFDa0Q7UUFDcEUsSUFBSTtZQUNGLE1BQU1RLFNBQVMsTUFBTSxJQUFJLENBQUNwQyxvQkFBb0IsQ0FBQ0Q7WUFFL0MsSUFBSSxDQUFDcUMsUUFBUTtnQkFDWCxPQUFPO29CQUFFQyxTQUFTO29CQUFPQyxRQUFRO2dCQUF3QztZQUMzRTtZQUVBLElBQUksQ0FBQ0YsT0FBT2hDLFFBQVEsRUFBRTtnQkFDcEIsT0FBTztvQkFBRWlDLFNBQVM7b0JBQU9DLFFBQVE7Z0JBQTJCO1lBQzlEO1lBRUEsTUFBTVQsZUFBZU8sT0FBT3hCLFVBQVUsQ0FBQ2UsUUFBUSxJQUFJO1lBQ25ELE1BQU1ZLFFBQVFILE9BQU92QixNQUFNLENBQUNjLFFBQVE7WUFDcEMsTUFBTWEsWUFBWUQsUUFBUVY7WUFFMUIsSUFBSUEsZUFBZUQsU0FBU1csT0FBTztnQkFDakMsT0FBTztvQkFDTEYsU0FBUztvQkFDVEMsUUFBUSxDQUFDLFVBQVUsRUFBRVgsUUFBUSxZQUFZLEVBQUVFLGFBQWEsQ0FBQyxFQUFFVSxNQUFNLENBQUMsQ0FBQztvQkFDbkVDLFdBQVdoQyxLQUFLQyxHQUFHLENBQUMsR0FBRytCO2dCQUN6QjtZQUNGO1lBRUEsT0FBTztnQkFDTEgsU0FBUztnQkFDVEcsV0FBV0EsWUFBWVo7WUFDekI7UUFFRixFQUFFLE9BQU9qRyxPQUFPO1lBQ2ROLFFBQVFNLEtBQUssQ0FBQyw2QkFBNkJBO1lBQzNDLE9BQU87Z0JBQUUwRyxTQUFTO2dCQUFPQyxRQUFRO1lBQWdCO1FBQ25EO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELGFBQWFHLHlCQUdWO1FBQ0QsSUFBSTtZQUNGcEgsUUFBUUMsR0FBRyxDQUFDO1lBRVosTUFBTTZFLE1BQU0sSUFBSWpFLE9BQU9TLFdBQVc7WUFFbEMsTUFBTSxFQUFFUyxNQUFNc0YsZUFBZSxFQUFFL0csS0FBSyxFQUFFLEdBQUcsTUFBTVosOERBQWFBLENBQ3pEaUgsSUFBSSxDQUFDLGlCQUNMVyxNQUFNLENBQUMsZUFDUFQsRUFBRSxDQUFDLHFCQUFxQixRQUN4QlUsRUFBRSxDQUFDLG1CQUFtQnpDLElBQ3ZCLGdGQUFnRjthQUMvRTBDLEdBQUcsQ0FBQyx1Q0FBdUM7WUFHOUMsSUFBSWxILE9BQU87Z0JBQ1QsTUFBTSxJQUFJQyxNQUFNLENBQUMsa0NBQWtDLEVBQUVELE1BQU1FLE9BQU8sRUFBRTtZQUN0RTtZQUVBLElBQUksQ0FBQzZHLG1CQUFtQkEsZ0JBQWdCSSxNQUFNLEtBQUssR0FBRztnQkFDcER6SCxRQUFRQyxHQUFHLENBQUM7Z0JBQ1osT0FBTztvQkFBRXlILFNBQVM7b0JBQUdDLFFBQVEsRUFBRTtnQkFBQztZQUNsQztZQUVBM0gsUUFBUUMsR0FBRyxDQUFDLENBQUMsZ0JBQWdCLEVBQUVvSCxnQkFBZ0JJLE1BQU0sQ0FBQyxnQ0FBZ0MsQ0FBQztZQUV2RixNQUFNRSxTQUFtQixFQUFFO1lBQzNCLElBQUlELFVBQVU7WUFFZCxLQUFLLE1BQU05QyxXQUFXeUMsZ0JBQWlCO2dCQUNyQyxJQUFJO29CQUNGLDZCQUE2QjtvQkFDN0IsTUFBTTNILDhEQUFhQSxDQUFDeUMsSUFBSSxDQUFDQyxLQUFLLENBQUN3RixjQUFjLENBQUNoRCxRQUFRaUQsT0FBTyxFQUFFO3dCQUM3REMsZUFBZTs0QkFBRUMsa0JBQWtCOzRCQUFNQyxpQkFBaUI7d0JBQXVCO29CQUNuRjtvQkFFQSw4QkFBOEI7b0JBQzlCLDREQUE0RDtvQkFDNUQsTUFBTSxFQUFFakcsTUFBTWtHLGtCQUFrQixFQUFFLEdBQUcsTUFBTXZJLDhEQUFhQSxDQUNyRGlILElBQUksQ0FBQyxpQkFDTFcsTUFBTSxDQUFDLGtCQUNQVCxFQUFFLENBQUMsV0FBV2pDLFFBQVFpRCxPQUFPLEVBQzdCdEQsTUFBTTtvQkFFVCxNQUFNMkQsZ0JBQWdCRCxvQkFBb0J6RSxrQkFBa0IsQ0FBQztvQkFFN0QsTUFBTTlELDhEQUFhQSxDQUNoQmlILElBQUksQ0FBQyxpQkFDTEMsTUFBTSxDQUFDO3dCQUNONUQsa0JBQWtCO3dCQUNsQlEsZ0JBQWdCOzRCQUNkLEdBQUcwRSxhQUFhOzRCQUNoQkgsa0JBQWtCOzRCQUNsQkksYUFBYSxJQUFJdEgsT0FBT1MsV0FBVzs0QkFDbkMwRyxpQkFBaUI7d0JBQ25CO29CQUNGLEdBQ0NuQixFQUFFLENBQUMsV0FBV2pDLFFBQVFpRCxPQUFPO29CQUVoQ0g7Z0JBRUYsRUFBRSxPQUFPVSxjQUFjO29CQUNyQixNQUFNQyxXQUFXLENBQUMsd0JBQXdCLEVBQUV6RCxRQUFRaUQsT0FBTyxDQUFDLEVBQUUsRUFBRU8sd0JBQXdCN0gsUUFBUTZILGFBQWE1SCxPQUFPLEdBQUc4SCxPQUFPRixlQUFlO29CQUM3SXBJLFFBQVFNLEtBQUssQ0FBQytIO29CQUNkVixPQUFPWSxJQUFJLENBQUNGO2dCQUNkO1lBQ0Y7WUFFQXJJLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHVCQUF1QixFQUFFeUgsUUFBUSxxQkFBcUIsRUFBRUMsT0FBT0YsTUFBTSxDQUFDLFFBQVEsQ0FBQztZQUU1RixPQUFPO2dCQUFFQztnQkFBU0M7WUFBTztRQUUzQixFQUFFLE9BQU9ySCxPQUFPO1lBQ2ROLFFBQVFNLEtBQUssQ0FBQyxtQ0FBbUNBO1lBQ2pELE9BQU87Z0JBQ0xvSCxTQUFTO2dCQUNUQyxRQUFRO29CQUFDckgsaUJBQWlCQyxRQUFRRCxNQUFNRSxPQUFPLEdBQUc7aUJBQW9CO1lBQ3hFO1FBQ0Y7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG5hYXRhXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXE9wb3NJXFx2MTZcXHNyY1xcbGliXFxzZXJ2aWNlc1xcZnJlZUFjY291bnRTZXJ2aWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy9saWIvc2VydmljZXMvZnJlZUFjY291bnRTZXJ2aWNlLnRzXG4vLyBTZXJ2aWNpbyBwYXJhIGdlc3Rpw7NuIGF1dG9tYXRpemFkYSBkZSBjdWVudGFzIGdyYXR1aXRhc1xuXG5pbXBvcnQgeyBTdXBhYmFzZUFkbWluU2VydmljZSwgRXh0ZW5kZWRVc2VyUHJvZmlsZSwgc3VwYWJhc2VBZG1pbiB9IGZyb20gJ0AvbGliL3N1cGFiYXNlL2FkbWluJztcbmltcG9ydCB7IGdldFBsYW5Db25maWd1cmF0aW9uLCBnZXRUb2tlbkxpbWl0Rm9yUGxhbiB9IGZyb20gJ0AvY29uZmlnL3BsYW5zJztcbmltcG9ydCB7IHJhbmRvbVVVSUQgfSBmcm9tICdjcnlwdG8nO1xuXG5leHBvcnQgaW50ZXJmYWNlIENyZWF0ZUZyZWVBY2NvdW50UmVxdWVzdCB7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIG5hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRnJlZUFjY291bnRSZXN1bHQge1xuICBzdWNjZXNzOiBib29sZWFuO1xuICB1c2VySWQ/OiBzdHJpbmc7XG4gIHByb2ZpbGVJZD86IHN0cmluZztcbiAgZXJyb3I/OiBzdHJpbmc7XG4gIGV4cGlyZXNBdD86IHN0cmluZztcbn1cblxuLy8gRnJlZUFjY291bnRTdGF0dXMgeSBvdHJhcyBpbnRlcmZhY2VzL2NsYXNlcyBubyBjYW1iaWFuXG5leHBvcnQgaW50ZXJmYWNlIEZyZWVBY2NvdW50U3RhdHVzIHtcbiAgaXNBY3RpdmU6IGJvb2xlYW47XG4gIGV4cGlyZXNBdDogc3RyaW5nIHwgbnVsbDtcbiAgZGF5c1JlbWFpbmluZzogbnVtYmVyO1xuICBob3Vyc1JlbWFpbmluZzogbnVtYmVyO1xuICB1c2FnZUNvdW50OiB7XG4gICAgZG9jdW1lbnRzOiBudW1iZXI7XG4gICAgdGVzdHM6IG51bWJlcjtcbiAgICBmbGFzaGNhcmRzOiBudW1iZXI7XG4gICAgbWluZE1hcHM6IG51bWJlcjtcbiAgICB0b2tlbnM6IG51bWJlcjtcbiAgfTtcbiAgbGltaXRzOiB7XG4gICAgZG9jdW1lbnRzOiBudW1iZXI7XG4gICAgdGVzdHM6IG51bWJlcjtcbiAgICBmbGFzaGNhcmRzOiBudW1iZXI7XG4gICAgbWluZE1hcHM6IG51bWJlcjtcbiAgICB0b2tlbnM6IG51bWJlcjtcbiAgfTtcbn1cblxuZXhwb3J0IGNsYXNzIEZyZWVBY2NvdW50U2VydmljZSB7XG5cbiAgLyoqXG4gICAqIENyZWFyIGN1ZW50YSBncmF0dWl0YSBhdXRvbcOhdGljYW1lbnRlXG4gICAqIEFob3JhIGNyZWEgZWwgdXN1YXJpbyBkaXJlY3RhbWVudGUgeSBnZW5lcmEgdW4gZW5sYWNlIGRlIHRpcG8gJ3JlY292ZXJ5JyBwYXJhIGVzdGFibGVjZXIgbGEgY29udHJhc2XDsWEuXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY3JlYXRlRnJlZUFjY291bnQocmVxdWVzdDogQ3JlYXRlRnJlZUFjY291bnRSZXF1ZXN0KTogUHJvbWlzZTxGcmVlQWNjb3VudFJlc3VsdD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+GkyBJbmljaWFuZG8gY3JlYWNpw7NuIGRlIGN1ZW50YSBncmF0dWl0YSAoZmx1am8gZGUgaW52aXRhY2nDs24pOicsIHJlcXVlc3QuZW1haWwpO1xuXG4gICAgICAvLyAxLiBWYWxpZGFyIHF1ZSBlbCBlbWFpbCBubyBlc3TDqSB5YSByZWdpc3RyYWRvXG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBleGlzdGluZ1VzZXIgPSBhd2FpdCBTdXBhYmFzZUFkbWluU2VydmljZS5nZXRVc2VyQnlFbWFpbChyZXF1ZXN0LmVtYWlsKTtcbiAgICAgICAgaWYgKGV4aXN0aW5nVXNlcikge1xuICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdFbCBlbWFpbCB5YSBlc3TDoSByZWdpc3RyYWRvIGVuIGVsIHNpc3RlbWEuJyB9O1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBpZiAoZXJyb3IgaW5zdGFuY2VvZiBFcnJvciAmJiBlcnJvci5tZXNzYWdlLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3VzZXIgbm90IGZvdW5kJykpIHtcbiAgICAgICAgICBjb25zb2xlLmxvZygnVXN1YXJpbyBubyBleGlzdGUgZW4gQXV0aCwgY29udGludWFuZG8gY29uIGxhIGludml0YWNpw7NuLicpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnNvbGUud2FybignQWR2ZXJ0ZW5jaWEgYWwgdmVyaWZpY2FyIHVzdWFyaW8gZXhpc3RlbnRlLCBzZSBjb250aW51YXLDoSBjb24gZWwgaW50ZW50byBkZSBpbnZpdGFjacOzbjonLCBlcnJvcik7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gMi4gQ2FsY3VsYXIgZmVjaGEgZGUgZXhwaXJhY2nDs24gKDUgZMOtYXMgZGVzZGUgYWhvcmEpXG4gICAgICBjb25zdCBleHBpcmVzQXQgPSBuZXcgRGF0ZSgpO1xuICAgICAgZXhwaXJlc0F0LnNldERhdGUoZXhwaXJlc0F0LmdldERhdGUoKSArIDUpO1xuXG4gICAgICAvLyAzLiBQcmVwYXJhciBtZXRhZGF0b3MgcGFyYSBlbCB1c3VhcmlvIGludml0YWRvXG4gICAgICBjb25zdCB1c2VyRGF0YUZvckNyZWF0aW9uID0ge1xuICAgICAgICBuYW1lOiByZXF1ZXN0Lm5hbWUgfHwgcmVxdWVzdC5lbWFpbC5zcGxpdCgnQCcpWzBdLFxuICAgICAgICBwbGFuOiAnZnJlZScsXG4gICAgICAgIGZyZWVfYWNjb3VudDogdHJ1ZSxcbiAgICAgICAgZXhwaXJlc19hdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgIGNyZWF0ZWRfdmlhOiAnZnJlZV9pbnZpdGF0aW9uX2Zsb3cnLFxuICAgICAgICByZWdpc3RyYXRpb25fdHlwZTogJ2F1dG9tYXRpY19mcmVlX2ludml0YXRpb24nLFxuICAgICAgICByZXF1aXJlc19wYXNzd29yZF9zZXR1cDogdHJ1ZVxuICAgICAgfTtcblxuICAgICAgY29uc29sZS5sb2coJ/Cfk6cgSW52aXRhbmRvIG51ZXZvIHVzdWFyaW8gY29uIGRhdG9zOicsIHtcbiAgICAgICAgZW1haWw6IHJlcXVlc3QuZW1haWwsXG4gICAgICAgIHVzZXJEYXRhOiB1c2VyRGF0YUZvckNyZWF0aW9uLFxuICAgICAgICByZWRpcmVjdFRvOiBgJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUFBfVVJMfS9hdXRoL2NvbmZpcm0taW52aXRhdGlvbmBcbiAgICAgIH0pO1xuXG4gICAgICAvLyA0LiBJbnZpdGFyIGFsIHVzdWFyaW8uIEVzdG8gY3JlYSBlbCB1c3VhcmlvIHkgZW52w61hIGVsIGVtYWlsIGRlIGludml0YWNpw7NuLlxuICAgICAgY29uc3QgeyBkYXRhOiB7IHVzZXI6IG5ld1VzZXIgfSwgZXJyb3I6IGludml0ZUVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4uaW52aXRlVXNlckJ5RW1haWwoXG4gICAgICAgIHJlcXVlc3QuZW1haWwsXG4gICAgICAgIHtcbiAgICAgICAgICBkYXRhOiB1c2VyRGF0YUZvckNyZWF0aW9uLFxuICAgICAgICAgIHJlZGlyZWN0VG86IGAke3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQUF9VUkx9L2F1dGgvY29uZmlybS1pbnZpdGF0aW9uYCxcbiAgICAgICAgfVxuICAgICAgKTtcblxuICAgICAgaWYgKGludml0ZUVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBpbnZpdGFuZG8gYWwgdXN1YXJpbzonLCBpbnZpdGVFcnJvcik7XG4gICAgICAgIGlmIChpbnZpdGVFcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdVc2VyIGFscmVhZHkgcmVnaXN0ZXJlZCcpKSB7XG4gICAgICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdZYSBleGlzdGUgdW5hIGN1ZW50YSBjb24gZXN0ZSBlbWFpbC4nIH07XG4gICAgICAgIH1cbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciBpbnZpdGFuZG8gYWwgdXN1YXJpbzogJHtpbnZpdGVFcnJvci5tZXNzYWdlfWApO1xuICAgICAgfVxuICAgICAgaWYgKCFuZXdVc2VyKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVXN1YXJpbyBubyBkZXZ1ZWx0byBkZXNwdcOpcyBkZSBsYSBpbnZpdGFjacOzbi4nKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coJ+KchSBVc3VhcmlvIGludml0YWRvIGV4aXRvc2FtZW50ZSBhIFN1cGFiYXNlIEF1dGg6JywgbmV3VXNlci5pZCk7XG5cbiAgICAgIC8vIDUuIENyZWFyIHBlcmZpbCBkZSB1c3VhcmlvIHkgcmVnaXN0cmFyIGhpc3RvcmlhbCBkZSBmb3JtYSBhdMOzbWljYVxuICAgICAgY29uc3QgcGxhbkNvbmZpZyA9IGdldFBsYW5Db25maWd1cmF0aW9uKCdmcmVlJyk7XG4gICAgICBpZiAoIXBsYW5Db25maWcpIHtcbiAgICAgICAgYXdhaXQgc3VwYWJhc2VBZG1pbi5hdXRoLmFkbWluLmRlbGV0ZVVzZXIobmV3VXNlci5pZCk7IC8vIENsZWFudXBcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdDb25maWd1cmFjacOzbiBkZSBwbGFuIGdyYXR1aXRvIG5vIGVuY29udHJhZGEnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY3VycmVudE1vbnRoID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNsaWNlKDAsIDcpICsgJy0wMSc7XG4gICAgICBjb25zdCBwcm9maWxlRGF0YUZvclJQQyA9IHtcbiAgICAgICAgc3Vic2NyaXB0aW9uX3BsYW46ICdmcmVlJyxcbiAgICAgICAgbW9udGhseV90b2tlbl9saW1pdDogZ2V0VG9rZW5MaW1pdEZvclBsYW4oJ2ZyZWUnKSxcbiAgICAgICAgY3VycmVudF9tb250aF90b2tlbnM6IDAsXG4gICAgICAgIGN1cnJlbnRfbW9udGg6IGN1cnJlbnRNb250aCxcbiAgICAgICAgcGF5bWVudF92ZXJpZmllZDogdHJ1ZSxcbiAgICAgICAgc3RyaXBlX2N1c3RvbWVyX2lkOiBudWxsLFxuICAgICAgICBzdHJpcGVfc3Vic2NyaXB0aW9uX2lkOiBudWxsLFxuICAgICAgICBsYXN0X3BheW1lbnRfZGF0ZTogbnVsbCxcbiAgICAgICAgYXV0b19yZW5ldzogZmFsc2UsXG4gICAgICAgIHBsYW5fZXhwaXJlc19hdDogZXhwaXJlc0F0LnRvSVNPU3RyaW5nKCksXG4gICAgICAgIHBsYW5fZmVhdHVyZXM6IHBsYW5Db25maWcuZmVhdHVyZXMsXG4gICAgICAgIHNlY3VyaXR5X2ZsYWdzOiB7XG4gICAgICAgICAgY3JlYXRlZF92aWFfZnJlZV9pbnZpdGF0aW9uX2Zsb3c6IHRydWUsXG4gICAgICAgICAgZnJlZV9hY2NvdW50OiB0cnVlLFxuICAgICAgICAgIGV4cGlyZXNfYXQ6IGV4cGlyZXNBdC50b0lTT1N0cmluZygpLFxuICAgICAgICAgIGFjdGl2YXRpb25fZGF0ZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgIHVzYWdlX2NvdW50OiB7IGRvY3VtZW50czogMCwgdGVzdHM6IDAsIGZsYXNoY2FyZHM6IDAsIG1pbmRNYXBzOiAwLCB0b2tlbnM6IDAgfVxuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICBjb25zdCB7IGRhdGE6IGNyZWF0aW9uUmVzdWx0LCBlcnJvcjogcnBjRXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgICAgLnJwYygnY3JlYXRlX3VzZXJfcHJvZmlsZV9hbmRfaGlzdG9yeScsIHtcbiAgICAgICAgICBwX3VzZXJfaWQ6IG5ld1VzZXIuaWQsXG4gICAgICAgICAgcF90cmFuc2FjdGlvbl9pZDogbnVsbCxcbiAgICAgICAgICBwX3Byb2ZpbGVfZGF0YTogcHJvZmlsZURhdGFGb3JSUENcbiAgICAgICAgfSlcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAocnBjRXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGFsIGVqZWN1dGFyIGxhIGZ1bmNpw7NuIFJQQyBjcmVhdGVfdXNlcl9wcm9maWxlX2FuZF9oaXN0b3J5OicsIHJwY0Vycm9yKTtcbiAgICAgICAgYXdhaXQgc3VwYWJhc2VBZG1pbi5hdXRoLmFkbWluLmRlbGV0ZVVzZXIobmV3VXNlci5pZCk7IC8vIENsZWFudXBcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBFcnJvciBlbiBsYSBjcmVhY2nDs24gYXTDs21pY2EgZGVsIHBlcmZpbDogJHtycGNFcnJvci5tZXNzYWdlfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBwcm9maWxlSWQgPSAoY3JlYXRpb25SZXN1bHQgYXMgYW55KS5jcmVhdGVkX3Byb2ZpbGVfaWQ7XG4gICAgICBjb25zb2xlLmxvZygn4pyFIFBlcmZpbCBncmF0dWl0byB5IGhpc3RvcmlhbCBjcmVhZG9zIGF0w7NtaWNhbWVudGUuIFByb2ZpbGUgSUQ6JywgcHJvZmlsZUlkKTtcblxuICAgICAgY29uc29sZS5sb2coJ/CfjokgQ3VlbnRhIGdyYXR1aXRhIGNyZWFkYSBleGl0b3NhbWVudGUgY29uIGZsdWpvIGRlIGludml0YWNpw7NuLicpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICB1c2VySWQ6IG5ld1VzZXIuaWQsXG4gICAgICAgIHByb2ZpbGVJZDogcHJvZmlsZUlkLFxuICAgICAgICBleHBpcmVzQXQ6IGV4cGlyZXNBdC50b0lTT1N0cmluZygpLFxuICAgICAgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgRXJyb3IgY3LDrXRpY28gZW4gbGEgY3JlYWNpw7NuIGRlIGN1ZW50YSBncmF0dWl0YTonLCBlcnJvcik7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBzdWNjZXNzOiBmYWxzZSxcbiAgICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0Vycm9yIGRlc2Nvbm9jaWRvIGFsIGNyZWFyIGN1ZW50YSBncmF0dWl0YSdcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLy8gTGFzIGZ1bmNpb25lcyBnZXRGcmVlQWNjb3VudFN0YXR1cywgaW5jcmVtZW50VXNhZ2VDb3VudCwgY2FuUGVyZm9ybUFjdGlvbiwgY2xlYW51cEV4cGlyZWRBY2NvdW50c1xuICAvLyBwZXJtYW5lY2VuIGlndWFsIHF1ZSBhbnRlcywgeWEgcXVlIHN1IGzDs2dpY2Egbm8gZGVwZW5kZSBkaXJlY3RhbWVudGUgZGUgY8OzbW8gc2UgY3Jlw7MgZWwgdXN1YXJpbyxcbiAgLy8gc2lubyBkZSBsb3MgZGF0b3MgZW4gdXNlcl9wcm9maWxlcy5cblxuICAvKipcbiAgICogVmVyaWZpY2FyIGVzdGFkbyBkZSBjdWVudGEgZ3JhdHVpdGFcbiAgICovXG4gIHN0YXRpYyBhc3luYyBnZXRGcmVlQWNjb3VudFN0YXR1cyh1c2VySWQ6IHN0cmluZyk6IFByb21pc2U8RnJlZUFjY291bnRTdGF0dXMgfCBudWxsPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHByb2ZpbGUgPSBhd2FpdCBTdXBhYmFzZUFkbWluU2VydmljZS5nZXRVc2VyUHJvZmlsZSh1c2VySWQpO1xuXG4gICAgICBpZiAoIXByb2ZpbGUgfHwgcHJvZmlsZS5zdWJzY3JpcHRpb25fcGxhbiAhPT0gJ2ZyZWUnKSB7XG4gICAgICAgIHJldHVybiBudWxsO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgY29uc3QgZXhwaXJlc0F0ID0gcHJvZmlsZS5wbGFuX2V4cGlyZXNfYXQgPyBuZXcgRGF0ZShwcm9maWxlLnBsYW5fZXhwaXJlc19hdCkgOiBudWxsO1xuXG4gICAgICBpZiAoIWV4cGlyZXNBdCkge1xuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cblxuICAgICAgY29uc3QgaXNBY3RpdmUgPSBub3cgPCBleHBpcmVzQXQ7XG4gICAgICBjb25zdCB0aW1lRGlmZiA9IGV4cGlyZXNBdC5nZXRUaW1lKCkgLSBub3cuZ2V0VGltZSgpO1xuICAgICAgY29uc3QgZGF5c1JlbWFpbmluZyA9IE1hdGgubWF4KDAsIE1hdGguY2VpbCh0aW1lRGlmZiAvICgxMDAwICogNjAgKiA2MCAqIDI0KSkpO1xuICAgICAgY29uc3QgaG91cnNSZW1haW5pbmcgPSBNYXRoLm1heCgwLCBNYXRoLmNlaWwodGltZURpZmYgLyAoMTAwMCAqIDYwICogNjApKSk7XG5cbiAgICAgIGNvbnN0IHVzYWdlQ291bnQgPSBwcm9maWxlLnNlY3VyaXR5X2ZsYWdzPy51c2FnZV9jb3VudCB8fCB7XG4gICAgICAgIGRvY3VtZW50czogMCwgdGVzdHM6IDAsIGZsYXNoY2FyZHM6IDAsIG1pbmRNYXBzOiAwLCB0b2tlbnM6IDBcbiAgICAgIH07XG4gICAgICB1c2FnZUNvdW50LnRva2VucyA9IHByb2ZpbGUuY3VycmVudF9tb250aF90b2tlbnMgfHwgMDtcblxuICAgICAgY29uc3QgcGxhbkNvbmZpZyA9IGdldFBsYW5Db25maWd1cmF0aW9uKCdmcmVlJyk7XG4gICAgICBjb25zdCBsaW1pdHMgPSB7XG4gICAgICAgIGRvY3VtZW50czogcGxhbkNvbmZpZz8ubGltaXRzLmRvY3VtZW50cyB8fCAxLFxuICAgICAgICB0ZXN0czogcGxhbkNvbmZpZz8ubGltaXRzLnRlc3RzRm9yVHJpYWwgfHwgMTAsXG4gICAgICAgIGZsYXNoY2FyZHM6IHBsYW5Db25maWc/LmxpbWl0cy5mbGFzaGNhcmRzRm9yVHJpYWwgfHwgMTAsXG4gICAgICAgIG1pbmRNYXBzOiBwbGFuQ29uZmlnPy5saW1pdHMubWluZE1hcHNGb3JUcmlhbCB8fCAyLFxuICAgICAgICB0b2tlbnM6IHBsYW5Db25maWc/LmxpbWl0cy50b2tlbnNGb3JUcmlhbCB8fCA1MDAwMFxuICAgICAgfTtcbiAgICAgIFxuICAgICAgLy8gQ2FsY3VsYXIgcHJvZ3Jlc3NQZXJjZW50YWdlXG4gICAgICBjb25zdCB0b3RhbER1cmF0aW9uID0gNSAqIDI0ICogNjAgKiA2MCAqIDEwMDA7IC8vIDUgZMOtYXMgZW4gbXNcbiAgICAgIGNvbnN0IGNyZWF0aW9uRGF0ZSA9IG5ldyBEYXRlKHByb2ZpbGUuY3JlYXRlZF9hdCB8fCBEYXRlLm5vdygpKTsgLy8gVXNhciBjcmVhdGVkX2F0IG8gbm93IHNpIG5vIGVzdMOhXG4gICAgICBjb25zdCBhY3RpdmF0aW9uRGF0ZSA9IHByb2ZpbGUuc2VjdXJpdHlfZmxhZ3M/LmFjdGl2YXRpb25fZGF0ZSA/IG5ldyBEYXRlKHByb2ZpbGUuc2VjdXJpdHlfZmxhZ3MuYWN0aXZhdGlvbl9kYXRlKSA6IGNyZWF0aW9uRGF0ZTtcbiAgICAgIGNvbnN0IHRpbWVFbGFwc2VkID0gbm93LmdldFRpbWUoKSAtIGFjdGl2YXRpb25EYXRlLmdldFRpbWUoKTtcbiAgICAgIGNvbnN0IHByb2dyZXNzUGVyY2VudGFnZSA9IE1hdGgubWluKDEwMCwgTWF0aC5tYXgoMCwgKHRpbWVFbGFwc2VkIC8gdG90YWxEdXJhdGlvbikgKiAxMDApKTtcblxuXG4gICAgICByZXR1cm4ge1xuICAgICAgICBpc0FjdGl2ZSxcbiAgICAgICAgZXhwaXJlc0F0OiBleHBpcmVzQXQudG9JU09TdHJpbmcoKSxcbiAgICAgICAgZGF5c1JlbWFpbmluZyxcbiAgICAgICAgaG91cnNSZW1haW5pbmcsXG4gICAgICAgIHVzYWdlQ291bnQsXG4gICAgICAgIGxpbWl0cyxcbiAgICAgICAgLy8gQHRzLWlnbm9yZSBBc2VndXJhciBxdWUgcHJvZ3Jlc3NQZXJjZW50YWdlIGVzdMOhIGVuIGVsIHRpcG8gc2kgZXMgbmVjZXNhcmlvXG4gICAgICAgIHByb2dyZXNzUGVyY2VudGFnZTogTWF0aC5yb3VuZChwcm9ncmVzc1BlcmNlbnRhZ2UpLFxuICAgICAgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBvYnRlbmllbmRvIGVzdGFkbyBkZSBjdWVudGEgZ3JhdHVpdGE6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEluY3JlbWVudGFyIGNvbnRhZG9yIGRlIHVzb1xuICAgKi9cbiAgc3RhdGljIGFzeW5jIGluY3JlbWVudFVzYWdlQ291bnQoXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgZmVhdHVyZTogJ2RvY3VtZW50cycgfCAndGVzdHMnIHwgJ2ZsYXNoY2FyZHMnIHwgJ21pbmRNYXBzJyxcbiAgICBhbW91bnQ6IG51bWJlciA9IDFcbiAgKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHByb2ZpbGUgPSBhd2FpdCBTdXBhYmFzZUFkbWluU2VydmljZS5nZXRVc2VyUHJvZmlsZSh1c2VySWQpO1xuXG4gICAgICBpZiAoIXByb2ZpbGUgfHwgcHJvZmlsZS5zdWJzY3JpcHRpb25fcGxhbiAhPT0gJ2ZyZWUnKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgY3VycmVudFVzYWdlID0gcHJvZmlsZS5zZWN1cml0eV9mbGFncz8udXNhZ2VfY291bnQgfHwge1xuICAgICAgICBkb2N1bWVudHM6IDAsIHRlc3RzOiAwLCBmbGFzaGNhcmRzOiAwLCBtaW5kTWFwczogMCwgdG9rZW5zOiAwXG4gICAgICB9O1xuXG4gICAgICBjdXJyZW50VXNhZ2VbZmVhdHVyZV0gPSAoY3VycmVudFVzYWdlW2ZlYXR1cmVdIHx8IDApICsgYW1vdW50O1xuXG4gICAgICBjb25zdCB1cGRhdGVEYXRhOiBQYXJ0aWFsPEV4dGVuZGVkVXNlclByb2ZpbGU+ID0ge1xuICAgICAgICBzZWN1cml0eV9mbGFnczoge1xuICAgICAgICAgIC4uLnByb2ZpbGUuc2VjdXJpdHlfZmxhZ3MsXG4gICAgICAgICAgdXNhZ2VfY291bnQ6IGN1cnJlbnRVc2FnZVxuICAgICAgICB9LFxuICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgIH07XG5cbiAgICAgIGF3YWl0IHN1cGFiYXNlQWRtaW5cbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAudXBkYXRlKHVwZGF0ZURhdGEpXG4gICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZCk7XG5cbiAgICAgIHJldHVybiB0cnVlO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluY3JlbWVudGFuZG8gY29udGFkb3IgZGUgdXNvOicsIGVycm9yKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVmVyaWZpY2FyIHNpIHNlIHB1ZWRlIHJlYWxpemFyIHVuYSBhY2Npw7NuXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY2FuUGVyZm9ybUFjdGlvbihcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICBmZWF0dXJlOiAnZG9jdW1lbnRzJyB8ICd0ZXN0cycgfCAnZmxhc2hjYXJkcycgfCAnbWluZE1hcHMnIHwgJ3Rva2VucycsXG4gICAgYW1vdW50OiBudW1iZXIgPSAxXG4gICk6IFByb21pc2U8eyBhbGxvd2VkOiBib29sZWFuOyByZWFzb24/OiBzdHJpbmc7IHJlbWFpbmluZz86IG51bWJlciB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHN0YXR1cyA9IGF3YWl0IHRoaXMuZ2V0RnJlZUFjY291bnRTdGF0dXModXNlcklkKTtcblxuICAgICAgaWYgKCFzdGF0dXMpIHtcbiAgICAgICAgcmV0dXJuIHsgYWxsb3dlZDogZmFsc2UsIHJlYXNvbjogJ0N1ZW50YSBubyBlbmNvbnRyYWRhIG8gbm8gZXMgZ3JhdHVpdGEnIH07XG4gICAgICB9XG5cbiAgICAgIGlmICghc3RhdHVzLmlzQWN0aXZlKSB7XG4gICAgICAgIHJldHVybiB7IGFsbG93ZWQ6IGZhbHNlLCByZWFzb246ICdDdWVudGEgZ3JhdHVpdGEgZXhwaXJhZGEnIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGN1cnJlbnRVc2FnZSA9IHN0YXR1cy51c2FnZUNvdW50W2ZlYXR1cmVdIHx8IDA7XG4gICAgICBjb25zdCBsaW1pdCA9IHN0YXR1cy5saW1pdHNbZmVhdHVyZV07XG4gICAgICBjb25zdCByZW1haW5pbmcgPSBsaW1pdCAtIGN1cnJlbnRVc2FnZTtcblxuICAgICAgaWYgKGN1cnJlbnRVc2FnZSArIGFtb3VudCA+IGxpbWl0KSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgYWxsb3dlZDogZmFsc2UsXG4gICAgICAgICAgcmVhc29uOiBgTMOtbWl0ZSBkZSAke2ZlYXR1cmV9IGFsY2FuemFkbyAoJHtjdXJyZW50VXNhZ2V9LyR7bGltaXR9KWAsXG4gICAgICAgICAgcmVtYWluaW5nOiBNYXRoLm1heCgwLCByZW1haW5pbmcpXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGFsbG93ZWQ6IHRydWUsXG4gICAgICAgIHJlbWFpbmluZzogcmVtYWluaW5nIC0gYW1vdW50XG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHZlcmlmaWNhbmRvIGFjY2nDs246JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgYWxsb3dlZDogZmFsc2UsIHJlYXNvbjogJ0Vycm9yIGludGVybm8nIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIExpbXBpYXIgY3VlbnRhcyBncmF0dWl0YXMgZXhwaXJhZGFzXG4gICAqL1xuICBzdGF0aWMgYXN5bmMgY2xlYW51cEV4cGlyZWRBY2NvdW50cygpOiBQcm9taXNlPHtcbiAgICBjbGVhbmVkOiBudW1iZXI7XG4gICAgZXJyb3JzOiBzdHJpbmdbXTtcbiAgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygn8J+nuSBJbmljaWFuZG8gbGltcGllemEgZGUgY3VlbnRhcyBncmF0dWl0YXMgZXhwaXJhZGFzJyk7XG5cbiAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKTtcblxuICAgICAgY29uc3QgeyBkYXRhOiBleHBpcmVkUHJvZmlsZXMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgIC5mcm9tKCd1c2VyX3Byb2ZpbGVzJylcbiAgICAgICAgLnNlbGVjdCgndXNlcl9pZCwgaWQnKVxuICAgICAgICAuZXEoJ3N1YnNjcmlwdGlvbl9wbGFuJywgJ2ZyZWUnKVxuICAgICAgICAubHQoJ3BsYW5fZXhwaXJlc19hdCcsIG5vdylcbiAgICAgICAgLy8gQcOxYWRpciBjb25kaWNpw7NuIHBhcmEgYXNlZ3VyYXIgcXVlIG5vIHNlIGRlc2FjdGl2ZW4gY3VlbnRhcyB5YSBkZXNoYWJpbGl0YWRhc1xuICAgICAgICAubmVxKCdzZWN1cml0eV9mbGFncyAtPj4gYWNjb3VudF9kaXNhYmxlZCcsICd0cnVlJyk7XG5cblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRXJyb3IgYnVzY2FuZG8gY3VlbnRhcyBleHBpcmFkYXM6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cblxuICAgICAgaWYgKCFleHBpcmVkUHJvZmlsZXMgfHwgZXhwaXJlZFByb2ZpbGVzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICBjb25zb2xlLmxvZygn4pyFIE5vIGhheSBjdWVudGFzIGV4cGlyYWRhcyBwYXJhIGxpbXBpYXInKTtcbiAgICAgICAgcmV0dXJuIHsgY2xlYW5lZDogMCwgZXJyb3JzOiBbXSB9O1xuICAgICAgfVxuXG4gICAgICBjb25zb2xlLmxvZyhg8J+Xke+4jyBFbmNvbnRyYWRhcyAke2V4cGlyZWRQcm9maWxlcy5sZW5ndGh9IGN1ZW50YXMgZXhwaXJhZGFzIHBhcmEgcHJvY2VzYXJgKTtcblxuICAgICAgY29uc3QgZXJyb3JzOiBzdHJpbmdbXSA9IFtdO1xuICAgICAgbGV0IGNsZWFuZWQgPSAwO1xuXG4gICAgICBmb3IgKGNvbnN0IHByb2ZpbGUgb2YgZXhwaXJlZFByb2ZpbGVzKSB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgLy8gRGVzYWN0aXZhciB1c3VhcmlvIGVuIGF1dGhcbiAgICAgICAgICBhd2FpdCBzdXBhYmFzZUFkbWluLmF1dGguYWRtaW4udXBkYXRlVXNlckJ5SWQocHJvZmlsZS51c2VyX2lkLCB7XG4gICAgICAgICAgICB1c2VyX21ldGFkYXRhOiB7IGFjY291bnRfZGlzYWJsZWQ6IHRydWUsIGRpc2FibGVkX3JlYXNvbjogJ2ZyZWVfYWNjb3VudF9leHBpcmVkJyB9XG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBNYXJjYXIgcGVyZmlsIGNvbW8gaW5hY3Rpdm9cbiAgICAgICAgICAvLyBPYnRlbmVyIHNlY3VyaXR5X2ZsYWdzIGV4aXN0ZW50ZXMgcGFyYSBubyBzb2JyZXNjcmliaXJsYXNcbiAgICAgICAgICBjb25zdCB7IGRhdGE6IGN1cnJlbnRQcm9maWxlRGF0YSB9ID0gYXdhaXQgc3VwYWJhc2VBZG1pblxuICAgICAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAgICAgLnNlbGVjdCgnc2VjdXJpdHlfZmxhZ3MnKVxuICAgICAgICAgICAgLmVxKCd1c2VyX2lkJywgcHJvZmlsZS51c2VyX2lkKVxuICAgICAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICAgICAgY29uc3QgZXhpc3RpbmdGbGFncyA9IGN1cnJlbnRQcm9maWxlRGF0YT8uc2VjdXJpdHlfZmxhZ3MgfHwge307XG5cbiAgICAgICAgICBhd2FpdCBzdXBhYmFzZUFkbWluXG4gICAgICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgICAgICAudXBkYXRlKHtcbiAgICAgICAgICAgICAgcGF5bWVudF92ZXJpZmllZDogZmFsc2UsIC8vIE1hcmNhciBwYWdvIGNvbW8gbm8gdmVyaWZpY2Fkb1xuICAgICAgICAgICAgICBzZWN1cml0eV9mbGFnczoge1xuICAgICAgICAgICAgICAgIC4uLmV4aXN0aW5nRmxhZ3MsIC8vIE1hbnRlbmVyIGZsYWdzIGV4aXN0ZW50ZXNcbiAgICAgICAgICAgICAgICBhY2NvdW50X2Rpc2FibGVkOiB0cnVlLFxuICAgICAgICAgICAgICAgIGRpc2FibGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgZGlzYWJsZWRfcmVhc29uOiAnZnJlZV9hY2NvdW50X2V4cGlyZWQnXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAuZXEoJ3VzZXJfaWQnLCBwcm9maWxlLnVzZXJfaWQpO1xuXG4gICAgICAgICAgY2xlYW5lZCsrO1xuXG4gICAgICAgIH0gY2F0Y2ggKGNsZWFudXBFcnJvcikge1xuICAgICAgICAgIGNvbnN0IGVycm9yTXNnID0gYEVycm9yIGxpbXBpYW5kbyB1c3VhcmlvICR7cHJvZmlsZS51c2VyX2lkfTogJHtjbGVhbnVwRXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGNsZWFudXBFcnJvci5tZXNzYWdlIDogU3RyaW5nKGNsZWFudXBFcnJvcil9YDtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yTXNnKTtcbiAgICAgICAgICBlcnJvcnMucHVzaChlcnJvck1zZyk7XG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYOKchSBMaW1waWV6YSBjb21wbGV0YWRhOiAke2NsZWFuZWR9IGN1ZW50YXMgcHJvY2VzYWRhcywgJHtlcnJvcnMubGVuZ3RofSBlcnJvcmVzYCk7XG5cbiAgICAgIHJldHVybiB7IGNsZWFuZWQsIGVycm9ycyB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBlbiBsaW1waWV6YSBkZSBjdWVudGFzOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNsZWFuZWQ6IDAsXG4gICAgICAgIGVycm9yczogW2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0Vycm9yIGRlc2Nvbm9jaWRvJ11cbiAgICAgIH07XG4gICAgfVxuICB9XG59Il0sIm5hbWVzIjpbIlN1cGFiYXNlQWRtaW5TZXJ2aWNlIiwic3VwYWJhc2VBZG1pbiIsImdldFBsYW5Db25maWd1cmF0aW9uIiwiZ2V0VG9rZW5MaW1pdEZvclBsYW4iLCJGcmVlQWNjb3VudFNlcnZpY2UiLCJjcmVhdGVGcmVlQWNjb3VudCIsInJlcXVlc3QiLCJjb25zb2xlIiwibG9nIiwiZW1haWwiLCJleGlzdGluZ1VzZXIiLCJnZXRVc2VyQnlFbWFpbCIsInN1Y2Nlc3MiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsInRvTG93ZXJDYXNlIiwiaW5jbHVkZXMiLCJ3YXJuIiwiZXhwaXJlc0F0IiwiRGF0ZSIsInNldERhdGUiLCJnZXREYXRlIiwidXNlckRhdGFGb3JDcmVhdGlvbiIsIm5hbWUiLCJzcGxpdCIsInBsYW4iLCJmcmVlX2FjY291bnQiLCJleHBpcmVzX2F0IiwidG9JU09TdHJpbmciLCJjcmVhdGVkX3ZpYSIsInJlZ2lzdHJhdGlvbl90eXBlIiwicmVxdWlyZXNfcGFzc3dvcmRfc2V0dXAiLCJ1c2VyRGF0YSIsInJlZGlyZWN0VG8iLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBQX1VSTCIsImRhdGEiLCJ1c2VyIiwibmV3VXNlciIsImludml0ZUVycm9yIiwiYXV0aCIsImFkbWluIiwiaW52aXRlVXNlckJ5RW1haWwiLCJpZCIsInBsYW5Db25maWciLCJkZWxldGVVc2VyIiwiY3VycmVudE1vbnRoIiwic2xpY2UiLCJwcm9maWxlRGF0YUZvclJQQyIsInN1YnNjcmlwdGlvbl9wbGFuIiwibW9udGhseV90b2tlbl9saW1pdCIsImN1cnJlbnRfbW9udGhfdG9rZW5zIiwiY3VycmVudF9tb250aCIsInBheW1lbnRfdmVyaWZpZWQiLCJzdHJpcGVfY3VzdG9tZXJfaWQiLCJzdHJpcGVfc3Vic2NyaXB0aW9uX2lkIiwibGFzdF9wYXltZW50X2RhdGUiLCJhdXRvX3JlbmV3IiwicGxhbl9leHBpcmVzX2F0IiwicGxhbl9mZWF0dXJlcyIsImZlYXR1cmVzIiwic2VjdXJpdHlfZmxhZ3MiLCJjcmVhdGVkX3ZpYV9mcmVlX2ludml0YXRpb25fZmxvdyIsImFjdGl2YXRpb25fZGF0ZSIsInVzYWdlX2NvdW50IiwiZG9jdW1lbnRzIiwidGVzdHMiLCJmbGFzaGNhcmRzIiwibWluZE1hcHMiLCJ0b2tlbnMiLCJjcmVhdGlvblJlc3VsdCIsInJwY0Vycm9yIiwicnBjIiwicF91c2VyX2lkIiwicF90cmFuc2FjdGlvbl9pZCIsInBfcHJvZmlsZV9kYXRhIiwic2luZ2xlIiwicHJvZmlsZUlkIiwiY3JlYXRlZF9wcm9maWxlX2lkIiwidXNlcklkIiwiZ2V0RnJlZUFjY291bnRTdGF0dXMiLCJwcm9maWxlIiwiZ2V0VXNlclByb2ZpbGUiLCJub3ciLCJpc0FjdGl2ZSIsInRpbWVEaWZmIiwiZ2V0VGltZSIsImRheXNSZW1haW5pbmciLCJNYXRoIiwibWF4IiwiY2VpbCIsImhvdXJzUmVtYWluaW5nIiwidXNhZ2VDb3VudCIsImxpbWl0cyIsInRlc3RzRm9yVHJpYWwiLCJmbGFzaGNhcmRzRm9yVHJpYWwiLCJtaW5kTWFwc0ZvclRyaWFsIiwidG9rZW5zRm9yVHJpYWwiLCJ0b3RhbER1cmF0aW9uIiwiY3JlYXRpb25EYXRlIiwiY3JlYXRlZF9hdCIsImFjdGl2YXRpb25EYXRlIiwidGltZUVsYXBzZWQiLCJwcm9ncmVzc1BlcmNlbnRhZ2UiLCJtaW4iLCJyb3VuZCIsImluY3JlbWVudFVzYWdlQ291bnQiLCJmZWF0dXJlIiwiYW1vdW50IiwiY3VycmVudFVzYWdlIiwidXBkYXRlRGF0YSIsInVwZGF0ZWRfYXQiLCJmcm9tIiwidXBkYXRlIiwiZXEiLCJjYW5QZXJmb3JtQWN0aW9uIiwic3RhdHVzIiwiYWxsb3dlZCIsInJlYXNvbiIsImxpbWl0IiwicmVtYWluaW5nIiwiY2xlYW51cEV4cGlyZWRBY2NvdW50cyIsImV4cGlyZWRQcm9maWxlcyIsInNlbGVjdCIsImx0IiwibmVxIiwibGVuZ3RoIiwiY2xlYW5lZCIsImVycm9ycyIsInVwZGF0ZVVzZXJCeUlkIiwidXNlcl9pZCIsInVzZXJfbWV0YWRhdGEiLCJhY2NvdW50X2Rpc2FibGVkIiwiZGlzYWJsZWRfcmVhc29uIiwiY3VycmVudFByb2ZpbGVEYXRhIiwiZXhpc3RpbmdGbGFncyIsImRpc2FibGVkX2F0IiwiY2xlYW51cEVycm9yIiwiZXJyb3JNc2ciLCJTdHJpbmciLCJwdXNoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/freeAccountService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/admin.ts":
/*!***********************************!*\
  !*** ./src/lib/supabase/admin.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAdminService: () => (/* binding */ SupabaseAdminService),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// src/lib/supabase/admin.ts\n// Cliente administrativo de Supabase para operaciones del servidor\n\n// Cliente admin con privilegios elevados\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\"https://fxnhpxjijinfuxxxplzj.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\n// Funciones de utilidad para operaciones administrativas\nclass SupabaseAdminService {\n    // Crear transacción de Stripe\n    static async createStripeTransaction(transaction) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').insert([\n            transaction\n        ]).select().single();\n        if (error) {\n            console.error('Error creating stripe transaction:', error);\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener transacción por session ID\n    static async getTransactionBySessionId(sessionId) {\n        const { data, error } = await supabaseAdmin.from('stripe_transactions').select('*').eq('stripe_session_id', sessionId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching transaction:', error);\n            throw new Error(`Failed to fetch transaction: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con invitación\n    static async createUserWithInvitation(email, userData) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user invitation:', {\n            email,\n            userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.inviteUserByEmail(email, {\n            data: userData,\n            redirectTo: `${\"http://localhost:3000\"}/auth/callback`\n        });\n        console.log('📊 [SUPABASE_ADMIN] Invitation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            userAud: data?.user?.aud,\n            userRole: data?.user?.role,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            appMetadata: data?.user?.app_metadata,\n            error: error?.message,\n            errorCode: error?.status,\n            fullError: error\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user invitation:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            throw new Error(`Failed to create user invitation: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear usuario con contraseña específica y opcionalmente enviar email de confirmación\n    static async createUserWithPassword(email, password, userData, sendConfirmationEmail = true) {\n        console.log('🔄 [SUPABASE_ADMIN] Creating user with password:', {\n            email,\n            userData,\n            sendConfirmationEmail,\n            timestamp: new Date().toISOString()\n        });\n        const { data, error } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            user_metadata: userData,\n            email_confirm: false // No confirmar automáticamente\n        });\n        console.log('📊 [SUPABASE_ADMIN] User creation result:', {\n            hasData: !!data,\n            hasUser: !!data?.user,\n            userId: data?.user?.id,\n            userEmail: data?.user?.email,\n            emailConfirmed: data?.user?.email_confirmed_at,\n            userMetadata: data?.user?.user_metadata,\n            error: error?.message,\n            errorCode: error?.status\n        });\n        if (error) {\n            console.error('❌ [SUPABASE_ADMIN] Error creating user with password:', {\n                message: error.message,\n                status: error.status,\n                details: error\n            });\n            return {\n                data: null,\n                error\n            };\n        }\n        // Enviar email de confirmación solo si se solicita\n        if (data?.user && sendConfirmationEmail) {\n            console.log('📧 Enviando email de confirmación...');\n            const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n                type: 'signup',\n                email: email,\n                password: password,\n                options: {\n                    redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n                }\n            });\n            if (emailError) {\n                console.error('⚠️ Error enviando email de confirmación:', emailError);\n            // No fallar completamente, el usuario puede confirmar manualmente\n            } else {\n                console.log('✅ Email de confirmación enviado exitosamente');\n            }\n        } else if (data?.user && !sendConfirmationEmail) {\n            console.log('📧 Email de confirmación omitido (se enviará después del pago)');\n        }\n        return {\n            data,\n            error: null\n        };\n    }\n    // Enviar email de confirmación para usuario existente\n    static async sendConfirmationEmailForUser(userId) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para usuario:', userId);\n        try {\n            // Obtener datos del usuario\n            const { data: userData, error: userError } = await supabaseAdmin.auth.admin.getUserById(userId);\n            if (userError || !userData?.user) {\n                console.error('Error obteniendo datos del usuario:', userError);\n                return {\n                    success: false,\n                    error: 'Usuario no encontrado'\n                };\n            }\n            const user = userData.user;\n            // Para usuarios pre-registrados, actualizar el estado de confirmación directamente\n            // ya que el pago exitoso confirma la intención del usuario\n            const { error: updateError } = await supabaseAdmin.auth.admin.updateUserById(user.id, {\n                email_confirm: true,\n                user_metadata: {\n                    ...user.user_metadata,\n                    payment_verified: true,\n                    email_confirmed_via_payment: true,\n                    confirmed_at: new Date().toISOString()\n                }\n            });\n            if (updateError) {\n                console.error('⚠️ Error confirmando email del usuario:', updateError);\n                return {\n                    success: false,\n                    error: updateError.message\n                };\n            }\n            console.log('✅ Usuario confirmado automáticamente después del pago exitoso');\n            return {\n                success: true\n            };\n        } catch (error) {\n            console.error('Error en sendConfirmationEmailForUser:', error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Error desconocido'\n            };\n        }\n    }\n    // Enviar email de confirmación para usuario existente (método legacy)\n    static async sendConfirmationEmail(email, password) {\n        console.log('📧 [SUPABASE_ADMIN] Enviando email de confirmación para:', email);\n        const { error: emailError } = await supabaseAdmin.auth.admin.generateLink({\n            type: 'signup',\n            email: email,\n            password: password,\n            options: {\n                redirectTo: `${\"http://localhost:3000\"}/auth/confirmed`\n            }\n        });\n        if (emailError) {\n            console.error('⚠️ Error enviando email de confirmación:', emailError);\n            return {\n                success: false,\n                error: emailError.message\n            };\n        } else {\n            console.log('✅ Email de confirmación enviado exitosamente');\n            return {\n                success: true\n            };\n        }\n    }\n    // Crear perfil de usuario\n    static async createUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').insert([\n            profile\n        ]).select().single();\n        if (error) {\n            console.error('Error creating user profile:', error);\n            throw new Error(`Failed to create user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Crear o actualizar perfil de usuario\n    static async upsertUserProfile(profile) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').upsert([\n            profile\n        ], {\n            onConflict: 'user_id'\n        }).select().single();\n        if (error) {\n            console.error('Error upserting user profile:', error);\n            throw new Error(`Failed to upsert user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar cambio de plan\n    static async logPlanChange(planChange) {\n        const { data, error } = await supabaseAdmin.from('user_plan_history').insert([\n            planChange\n        ]).select().single();\n        if (error) {\n            console.error('Error logging plan change:', error);\n            throw new Error(`Failed to log plan change: ${error.message}`);\n        }\n        return data;\n    }\n    // Registrar acceso a característica\n    static async logFeatureAccess(accessLog) {\n        const { data, error } = await supabaseAdmin.from('feature_access_log').insert([\n            accessLog\n        ]).select().single();\n        if (error) {\n            console.error('Error logging feature access:', error);\n            throw new Error(`Failed to log feature access: ${error.message}`);\n        }\n        return data;\n    }\n    // Obtener perfil de usuario por ID\n    static async getUserProfile(userId) {\n        const { data, error } = await supabaseAdmin.from('user_profiles').select('*').eq('user_id', userId).single();\n        if (error && error.code !== 'PGRST116') {\n            console.error('Error fetching user profile:', error);\n            throw new Error(`Failed to fetch user profile: ${error.message}`);\n        }\n        return data;\n    }\n    // Actualizar transacción con user_id\n    static async updateTransactionWithUser(transactionId, userId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            user_id: userId,\n            updated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error updating transaction with user_id:', error);\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n    }\n    // Activar transacción (marcar como activada)\n    static async activateTransaction(transactionId) {\n        const { error } = await supabaseAdmin.from('stripe_transactions').update({\n            activated_at: new Date().toISOString()\n        }).eq('id', transactionId);\n        if (error) {\n            console.error('Error activating transaction:', error);\n            throw new Error(`Failed to activate transaction: ${error.message}`);\n        }\n    }\n    // Obtener conteo de documentos del usuario\n    static async getDocumentsCount(userId) {\n        const { count, error } = await supabaseAdmin.from('documentos').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', userId);\n        if (error) {\n            console.error('Error getting documents count:', error);\n            return 0; // Retornar 0 en caso de error en lugar de lanzar excepción\n        }\n        return count || 0;\n    }\n    // Obtener usuario por email desde Supabase Auth\n    static async getUserByEmail(email) {\n        try {\n            const { data: { users }, error } = await supabaseAdmin.auth.admin.listUsers();\n            if (error) {\n                console.error('Error getting user by email:', error);\n                throw new Error(`Failed to get user by email: ${error.message}`);\n            }\n            if (!users || users.length === 0) {\n                return null;\n            }\n            // Filtrar por email ya que la API no permite filtro directo\n            const user = users.find((u)=>u.email === email);\n            if (!user) {\n                return null;\n            }\n            return {\n                id: user.id,\n                email: user.email,\n                email_confirmed_at: user.email_confirmed_at\n            };\n        } catch (error) {\n            console.error('Error in getUserByEmail:', error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/admin.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fauth%2Ffree-account-status%2Froute&page=%2Fapi%2Fauth%2Ffree-account-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Ffree-account-status%2Froute.ts&appDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cnaata%5CDocuments%5Caugment-projects%5COposI%5Cv16&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();