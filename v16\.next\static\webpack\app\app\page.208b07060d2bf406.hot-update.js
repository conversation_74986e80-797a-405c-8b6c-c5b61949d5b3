"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPlus,FiPrinter,FiRefreshCw,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/config/plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n/* harmony import */ var _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/hooks/useUserPlan */ \"(app-pages-browser)/./src/hooks/useUserPlan.ts\");\n// ===== Archivo: src\\app\\app\\page.tsx (Corregido) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // <-- MODIFICACIÓN 1\n // <-- MODIFICACIÓN 2: Importación corregida\n\n\n\n\n\n\nfunction AppPage() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTokenStats, setShowTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshSummaries, setRefreshSummaries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStudyPlanningAccess, setHasStudyPlanningAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasAiTutorAccess, setHasAiTutorAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSummaryAccess, setHasSummaryAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shouldRefreshTokenStats, setShouldRefreshTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plan: userPlan, isLoading: planLoading } = (0,_hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan)();\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration)();\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults)({\n        onResult: {\n            \"AppPage.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"AppPage.usePlanEstudiosResults\"],\n        onError: {\n            \"AppPage.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"AppPage.usePlanEstudiosResults\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (isLoading || planLoading || !user) {\n                return;\n            }\n            const checkInitialSetup = {\n                \"AppPage.useEffect.checkInitialSetup\": async ()=>{\n                    try {\n                        // LÓGICA CLAVE: Solo verificar temario para usuarios pro\n                        if (userPlan === 'pro') {\n                            const temarioConfigurado = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.tieneTemarioConfigurado)();\n                            if (!temarioConfigurado) {\n                                setMostrarSetupTemario(true);\n                                return;\n                            }\n                        }\n                        // Para planes 'free' y 'usuario' ir directamente al dashboard\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    } catch (error) {\n                        console.error('Error en checkInitialSetup:', error);\n                        // En caso de error, ir al dashboard por defecto (comportamiento seguro)\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    }\n                }\n            }[\"AppPage.useEffect.checkInitialSetup\"];\n            checkInitialSetup();\n            checkAccessFeatures();\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        userPlan,\n        planLoading\n    ]);\n    const cargarDatosDashboard = async ()=>{\n        if (!user) return;\n        try {\n            const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.obtenerTemarioUsuario)();\n            if (temario) {\n                setTemarioId(temario.id);\n                const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__.tienePlanificacionConfigurada)(temario.id);\n                setTienePlanificacion(tienePlan);\n                const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                if (planExistente === null || planExistente === void 0 ? void 0 : planExistente.plan_data) {\n                    setPlanEstudios(planExistente.plan_data);\n                } else {\n                    setPlanEstudios(null);\n                }\n            } else {\n                setTemarioId(null);\n                setTienePlanificacion(false);\n                setPlanEstudios(null);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        }\n    };\n    const checkAccessFeatures = async ()=>{\n        if (user) {\n            const [studyPlanningAccess, aiTutorAccess, summaryAccess] = await Promise.all([\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('study_planning'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('ai_tutor_chat'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('summary_a1_a2')\n            ]);\n            setHasStudyPlanningAccess(studyPlanningAccess);\n            setHasAiTutorAccess(aiTutorAccess);\n            setHasSummaryAccess(summaryAccess);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    if (isLoading || planLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 151,\n            columnNumber: 7\n        }, this);\n    }\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        cargarDatosDashboard();\n    };\n    if (mostrarSetupTemario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 166,\n            columnNumber: 12\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>setPlanEstudios(result),\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const handleNavigateToTab = (tab)=>{\n        setActiveTab(tab);\n    };\n    const handleSummaryGenerated = (summaryId)=>{\n        setRefreshSummaries((prev)=>prev + 1);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center -ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo2.png\",\n                                        alt: \"OposiAI Logo\",\n                                        width: 80,\n                                        height: 80,\n                                        className: \"h-20 w-20 object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/profile'),\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTokenStats(true),\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        title: \"Ver estad\\xedsticas de uso de IA\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiBarChart, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPlus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nuevo documento\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 335,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiFileText, {\n                                        className: \"w-4 h-4 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-base font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 473,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 480,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 476,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 487,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 483,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 498,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 508,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 513,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 521,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 516,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 537,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 52\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 553,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 554,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 555,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 569,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 579,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 590,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 597,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 589,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 588,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 587,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: ()=>{\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false);\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 608,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"nDqoKpz+OWY9Y6K3b7WWUu4YOYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});