// src/components/testing/AppFlowTester.tsx
// Componente para testing del flujo de inicio de aplicación

import React, { useState } from 'react';
import { FiPlay, FiCheck, FiX, FiInfo } from 'react-icons/fi';

interface TestCase {
  id: string;
  name: string;
  description: string;
  userPlan: 'free' | 'usuario' | 'pro';
  hasTemario: boolean;
  expectedBehavior: 'dashboard' | 'temario-setup';
  status: 'pending' | 'running' | 'passed' | 'failed';
  result?: string;
}

const AppFlowTester: React.FC = () => {
  const [testCases, setTestCases] = useState<TestCase[]>([
    {
      id: 'free-user',
      name: 'Usuario Plan Free',
      description: 'Usuario con plan gratuito debe ir directamente al dashboard',
      userPlan: 'free',
      hasTemario: false,
      expectedBehavior: 'dashboard',
      status: 'pending'
    },
    {
      id: 'usuario-user',
      name: 'Usuario Plan Usuario',
      description: 'Usuario con plan "usuario" debe ir directamente al dashboard',
      userPlan: 'usuario',
      hasTemario: false,
      expectedBehavior: 'dashboard',
      status: 'pending'
    },
    {
      id: 'pro-without-temario',
      name: 'Usuario Pro Sin Temario',
      description: 'Usuario pro sin temario debe ver la configuración de temario',
      userPlan: 'pro',
      hasTemario: false,
      expectedBehavior: 'temario-setup',
      status: 'pending'
    },
    {
      id: 'pro-with-temario',
      name: 'Usuario Pro Con Temario',
      description: 'Usuario pro con temario debe ir directamente al dashboard',
      userPlan: 'pro',
      hasTemario: true,
      expectedBehavior: 'dashboard',
      status: 'pending'
    }
  ]);

  const simulateLogic = (userPlan: string, hasTemario: boolean): 'dashboard' | 'temario-setup' => {
    // Simular la lógica exacta de AppPage
    if (userPlan === 'pro' && !hasTemario) {
      return 'temario-setup';
    } else {
      return 'dashboard';
    }
  };

  const runTest = async (testCase: TestCase) => {
    setTestCases(prev => prev.map(tc => 
      tc.id === testCase.id 
        ? { ...tc, status: 'running' }
        : tc
    ));

    // Simular delay de red
    await new Promise(resolve => setTimeout(resolve, 500));

    const actualBehavior = simulateLogic(testCase.userPlan, testCase.hasTemario);
    const passed = actualBehavior === testCase.expectedBehavior;

    setTestCases(prev => prev.map(tc => 
      tc.id === testCase.id 
        ? { 
            ...tc, 
            status: passed ? 'passed' : 'failed',
            result: `Esperado: ${testCase.expectedBehavior}, Obtenido: ${actualBehavior}`
          }
        : tc
    ));
  };

  const runAllTests = async () => {
    for (const testCase of testCases) {
      await runTest(testCase);
    }
  };

  const resetTests = () => {
    setTestCases(prev => prev.map(tc => ({ 
      ...tc, 
      status: 'pending' as const,
      result: undefined
    })));
  };

  const getStatusIcon = (status: TestCase['status']) => {
    switch (status) {
      case 'passed':
        return <FiCheck className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <FiX className="w-5 h-5 text-red-500" />;
      case 'running':
        return <div className="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />;
      default:
        return <FiInfo className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: TestCase['status']) => {
    switch (status) {
      case 'passed':
        return 'bg-green-50 border-green-200';
      case 'failed':
        return 'bg-red-50 border-red-200';
      case 'running':
        return 'bg-blue-50 border-blue-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const passedTests = testCases.filter(tc => tc.status === 'passed').length;
  const totalTests = testCases.length;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Test del Flujo de Inicio de Aplicación
            </h2>
            <p className="text-gray-600 mt-1">
              Verificación de la lógica de redirección según el plan del usuario
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={runAllTests}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiPlay className="w-4 h-4" />
              Ejecutar Todos
            </button>
            <button
              onClick={resetTests}
              className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Reiniciar
            </button>
          </div>
        </div>

        {/* Resumen de resultados */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">
              Progreso de Tests
            </span>
            <span className="text-sm text-gray-600">
              {passedTests}/{totalTests} pasados
            </span>
          </div>
          <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(passedTests / totalTests) * 100}%` }}
            />
          </div>
        </div>

        {/* Lista de test cases */}
        <div className="space-y-4">
          {testCases.map((testCase) => (
            <div
              key={testCase.id}
              className={`border rounded-lg p-4 transition-all duration-200 ${getStatusColor(testCase.status)}`}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    {getStatusIcon(testCase.status)}
                    <h3 className="font-semibold text-gray-900">
                      {testCase.name}
                    </h3>
                  </div>
                  <p className="text-gray-600 text-sm mb-3">
                    {testCase.description}
                  </p>
                  <div className="flex gap-4 text-xs">
                    <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                      Plan: {testCase.userPlan}
                    </span>
                    <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded">
                      Temario: {testCase.hasTemario ? 'Sí' : 'No'}
                    </span>
                    <span className="px-2 py-1 bg-green-100 text-green-800 rounded">
                      Esperado: {testCase.expectedBehavior}
                    </span>
                  </div>
                  {testCase.result && (
                    <div className="mt-2 text-xs text-gray-600">
                      {testCase.result}
                    </div>
                  )}
                </div>
                <button
                  onClick={() => runTest(testCase)}
                  disabled={testCase.status === 'running'}
                  className="ml-4 px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  Ejecutar
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Información adicional */}
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">
            Lógica de Redirección
          </h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p>• <strong>Plan 'pro' sin temario:</strong> Mostrar configuración de temario</p>
            <p>• <strong>Plan 'pro' con temario:</strong> Ir al dashboard</p>
            <p>• <strong>Plan 'free' o 'usuario':</strong> Ir al dashboard (sin acceso a planes de estudio)</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppFlowTester;
