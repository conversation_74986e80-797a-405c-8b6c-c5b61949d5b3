// src/lib/services/limitHandler.ts
// Manejo de límites alcanzados y notificaciones

import { createClient } from '@/lib/supabase/supabaseClient';
import { getPlanConfiguration } from '@/config/plans';
import { WebhookLogger } from '@/lib/utils/webhookLogger';

export interface LimitStatus {
  type: 'tokens' | 'feature' | 'plan';
  severity: 'warning' | 'limit_reached' | 'exceeded';
  current: number;
  limit: number;
  percentage: number;
  message: string;
  actionRequired: boolean;
  suggestedAction?: string;
  upgradeOptions?: {
    plan: string;
    benefits: string[];
    newLimit: number;
  }[];
}

export interface LimitNotification {
  userId: string;
  type: string;
  severity: 'info' | 'warning' | 'error';
  title: string;
  message: string;
  actionUrl?: string;
  actionText?: string;
  metadata?: any;
}

export class LimitHandler {
  
  /**
   * Verificar estado de límites del usuario (versión servidor)
   */
  static async checkUserLimits(userId: string): Promise<LimitStatus[]> {
    try {
      // Import dinámico para evitar problemas en el cliente
      const { SupabaseAdminService } = await import('@/lib/supabase/admin');
      const profile = await SupabaseAdminService.getUserProfile(userId);

      if (!profile) {
        return [];
      }

      const limits: LimitStatus[] = [];

      // Verificar límites de tokens
      const tokenStatus = await this.checkTokenLimits(profile);
      if (tokenStatus) {
        limits.push(tokenStatus);
      }

      // Verificar límites de plan
      const planStatus = await this.checkPlanLimits(profile);
      if (planStatus) {
        limits.push(planStatus);
      }

      return limits;

    } catch (error) {
      console.error('Error checking user limits:', error);
      return [];
    }
  }

  /**
   * Verificar estado de límites del usuario (versión cliente - NO usa supabaseAdmin)
   */
  static async checkClientUserLimits(): Promise<LimitStatus[]> {
    try {
      const supabase = createClient();

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return [];
      }

      // Obtener perfil usando cliente normal
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('subscription_plan, payment_verified, monthly_token_limit, current_month_tokens, current_month, plan_expires_at')
        .eq('user_id', user.id)
        .single();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error("Error fetching profile for limits check:", profileError);
        return [];
      }

      if (!profile) {
        return [];
      }

      const limits: LimitStatus[] = [];

      // Verificar límites de tokens
      const tokenStatus = await this.checkTokenLimits(profile);
      if (tokenStatus) {
        limits.push(tokenStatus);
      }

      // Verificar límites de plan
      const planStatus = await this.checkPlanLimits(profile);
      if (planStatus) {
        limits.push(planStatus);
      }

      return limits;

    } catch (error) {
      console.error('Error checking client user limits:', error);
      return [];
    }
  }

  /**
   * Verificar límites de tokens
   */
  private static async checkTokenLimits(profile: any): Promise<LimitStatus | null> {
    const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
    const currentTokens = profile.current_month === currentMonth 
      ? profile.current_month_tokens 
      : 0;

    const percentage = (currentTokens / profile.monthly_token_limit) * 100;

    // Determinar severidad
    let severity: 'warning' | 'limit_reached' | 'exceeded' = 'warning';
    let message = '';
    let actionRequired = false;
    let suggestedAction = '';

    if (percentage >= 100) {
      severity = 'exceeded';
      message = `Has excedido tu límite mensual de tokens (${currentTokens.toLocaleString()}/${profile.monthly_token_limit.toLocaleString()})`;
      actionRequired = true;
      suggestedAction = 'Actualiza tu plan para obtener más tokens';
    } else if (percentage >= 90) {
      severity = 'limit_reached';
      message = `Estás cerca de tu límite mensual de tokens (${Math.round(percentage)}% usado)`;
      actionRequired = true;
      suggestedAction = 'Considera actualizar tu plan antes de alcanzar el límite';
    } else if (percentage >= 75) {
      severity = 'warning';
      message = `Has usado ${Math.round(percentage)}% de tus tokens mensuales`;
      actionRequired = false;
      suggestedAction = 'Monitorea tu uso para evitar alcanzar el límite';
    } else {
      // No hay problema con los tokens
      return null;
    }

    // Obtener opciones de upgrade
    const upgradeOptions = this.getUpgradeOptions(profile.subscription_plan);

    return {
      type: 'tokens',
      severity,
      current: currentTokens,
      limit: profile.monthly_token_limit,
      percentage: Math.round(percentage),
      message,
      actionRequired,
      suggestedAction,
      upgradeOptions
    };
  }

  /**
   * Verificar límites de plan
   */
  private static async checkPlanLimits(profile: any): Promise<LimitStatus | null> {
    // Verificar si el pago está pendiente para planes de pago
    if (profile.subscription_plan !== 'free' && !profile.payment_verified) {
      return {
        type: 'plan',
        severity: 'exceeded',
        current: 0,
        limit: 1,
        percentage: 0,
        message: 'Tu pago está pendiente de verificación',
        actionRequired: true,
        suggestedAction: 'Completa el proceso de pago para activar tu plan',
        upgradeOptions: []
      };
    }

    // Verificar si el plan ha expirado (si aplica)
    if (profile.plan_expires_at) {
      const expirationDate = new Date(profile.plan_expires_at);
      const now = new Date();
      const daysUntilExpiration = Math.ceil((expirationDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

      if (daysUntilExpiration <= 0) {
        return {
          type: 'plan',
          severity: 'exceeded',
          current: 0,
          limit: 1,
          percentage: 0,
          message: 'Tu plan ha expirado',
          actionRequired: true,
          suggestedAction: 'Renueva tu suscripción para continuar usando las funciones premium',
          upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)
        };
      } else if (daysUntilExpiration <= 7) {
        return {
          type: 'plan',
          severity: 'warning',
          current: daysUntilExpiration,
          limit: 30,
          percentage: Math.round(((30 - daysUntilExpiration) / 30) * 100),
          message: `Tu plan expira en ${daysUntilExpiration} día${daysUntilExpiration !== 1 ? 's' : ''}`,
          actionRequired: false,
          suggestedAction: 'Renueva tu suscripción para evitar la interrupción del servicio',
          upgradeOptions: this.getUpgradeOptions(profile.subscription_plan)
        };
      }
    }

    return null;
  }

  /**
   * Obtener opciones de upgrade disponibles
   */
  private static getUpgradeOptions(currentPlan: string) {
    const upgradeOptions: any[] = [];

    if (currentPlan === 'free') {
      const usuarioPlan = getPlanConfiguration('usuario');
      const proPlan = getPlanConfiguration('pro');

      if (usuarioPlan) {
        const monthlyTokens = usuarioPlan.limits.monthlyTokens || 1000000;
        upgradeOptions.push({
          plan: 'usuario',
          benefits: [
            'Chat con preparador IA',
            `${monthlyTokens.toLocaleString()} tokens mensuales`,
            'Tests y flashcards ilimitados'
          ],
          newLimit: monthlyTokens
        });
      }

      if (proPlan) {
        const monthlyTokens = proPlan.limits.monthlyTokens || 1000000;
        upgradeOptions.push({
          plan: 'pro',
          benefits: [
            'Todas las funciones del plan Usuario',
            'Planificación de estudios con IA',
            'Resúmenes A1 y A2',
            `${monthlyTokens.toLocaleString()} tokens mensuales`
          ],
          newLimit: monthlyTokens
        });
      }
    } else if (currentPlan === 'usuario') {
      const proPlan = getPlanConfiguration('pro');

      if (proPlan) {
        const monthlyTokens = proPlan.limits.monthlyTokens || 1000000;
        upgradeOptions.push({
          plan: 'pro',
          benefits: [
            'Planificación de estudios con IA',
            'Resúmenes A1 y A2',
            'Funciones avanzadas',
            `${monthlyTokens.toLocaleString()} tokens mensuales`
          ],
          newLimit: monthlyTokens
        });
      }
    }

    return upgradeOptions;
  }

  /**
   * Crear notificación de límite
   */
  static async createLimitNotification(
    userId: string,
    limitStatus: LimitStatus
  ): Promise<LimitNotification> {
    const notification: LimitNotification = {
      userId,
      type: `limit_${limitStatus.type}`,
      severity: limitStatus.severity === 'exceeded' ? 'error' : 
               limitStatus.severity === 'limit_reached' ? 'warning' : 'info',
      title: this.getNotificationTitle(limitStatus),
      message: limitStatus.message,
      metadata: {
        limitType: limitStatus.type,
        current: limitStatus.current,
        limit: limitStatus.limit,
        percentage: limitStatus.percentage
      }
    };

    // Agregar acción si es necesaria
    if (limitStatus.actionRequired && limitStatus.upgradeOptions && limitStatus.upgradeOptions.length > 0) {
      notification.actionUrl = '/payment';
      notification.actionText = 'Actualizar Plan';
    }

    // Log de la notificación
    await WebhookLogger.logFeatureAccess(
      userId,
      `limit_notification_${limitStatus.type}`,
      false,
      'system',
      0,
      `Limit notification: ${limitStatus.severity}`
    );

    return notification;
  }

  /**
   * Obtener título de notificación según el tipo de límite
   */
  private static getNotificationTitle(limitStatus: LimitStatus): string {
    switch (limitStatus.type) {
      case 'tokens':
        if (limitStatus.severity === 'exceeded') {
          return 'Límite de tokens excedido';
        } else if (limitStatus.severity === 'limit_reached') {
          return 'Límite de tokens casi alcanzado';
        } else {
          return 'Uso elevado de tokens';
        }
      
      case 'plan':
        if (limitStatus.severity === 'exceeded') {
          return 'Plan expirado o pago pendiente';
        } else {
          return 'Plan próximo a expirar';
        }
      
      default:
        return 'Límite alcanzado';
    }
  }

  /**
   * Verificar si una acción está bloqueada por límites (versión servidor)
   */
  static async isActionBlocked(
    userId: string,
    action: string,
    tokensRequired: number = 0
  ): Promise<{
    blocked: boolean;
    reason?: string;
    limitStatus?: LimitStatus;
  }> {
    try {
      const limits = await this.checkUserLimits(userId);

      // Verificar límites de tokens si se requieren
      if (tokensRequired > 0) {
        const tokenLimit = limits.find(l => l.type === 'tokens');

        if (tokenLimit && tokenLimit.severity === 'exceeded') {
          return {
            blocked: true,
            reason: 'Límite mensual de tokens excedido',
            limitStatus: tokenLimit
          };
        }

        if (tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit) {
          return {
            blocked: true,
            reason: `Esta acción requiere ${tokensRequired} tokens pero solo tienes ${tokenLimit.limit - tokenLimit.current} disponibles`,
            limitStatus: tokenLimit
          };
        }
      }

      // Verificar límites de plan
      const planLimit = limits.find(l => l.type === 'plan' && l.severity === 'exceeded');

      if (planLimit) {
        return {
          blocked: true,
          reason: planLimit.message,
          limitStatus: planLimit
        };
      }

      return { blocked: false };

    } catch (error) {
      console.error('Error checking if action is blocked:', error);
      return {
        blocked: true,
        reason: 'Error verificando límites'
      };
    }
  }

  /**
   * Verificar si una acción está bloqueada por límites (versión cliente - NO usa supabaseAdmin)
   */
  static async isClientActionBlocked(
    action: string,
    tokensRequired: number = 0
  ): Promise<{
    blocked: boolean;
    reason?: string;
    limitStatus?: LimitStatus;
  }> {
    try {
      const limits = await this.checkClientUserLimits();

      // Verificar límites de tokens si se requieren
      if (tokensRequired > 0) {
        const tokenLimit = limits.find(l => l.type === 'tokens');

        if (tokenLimit && tokenLimit.severity === 'exceeded') {
          return {
            blocked: true,
            reason: 'Límite mensual de tokens excedido',
            limitStatus: tokenLimit
          };
        }

        if (tokenLimit && tokenLimit.current + tokensRequired > tokenLimit.limit) {
          return {
            blocked: true,
            reason: `Esta acción requiere ${tokensRequired} tokens pero solo tienes ${tokenLimit.limit - tokenLimit.current} disponibles`,
            limitStatus: tokenLimit
          };
        }
      }

      // Verificar límites de plan
      const planLimit = limits.find(l => l.type === 'plan' && l.severity === 'exceeded');

      if (planLimit) {
        return {
          blocked: true,
          reason: planLimit.message,
          limitStatus: planLimit
        };
      }

      return { blocked: false };

    } catch (error) {
      console.error('Error checking if client action is blocked:', error);
      return {
        blocked: true,
        reason: 'Error verificando límites'
      };
    }
  }

  /**
   * Registrar uso después de una acción exitosa (versión servidor)
   */
  static async recordUsage(
    userId: string,
    action: string,
    tokensUsed: number
  ): Promise<void> {
    try {
      if (tokensUsed > 0) {
        // Import dinámico para evitar problemas en el cliente
        const { SupabaseAdminService } = await import('@/lib/supabase/admin');

        // Actualizar contador de tokens
        const profile = await SupabaseAdminService.getUserProfile(userId);

        if (profile) {
          const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
          const currentTokens = profile.current_month === currentMonth
            ? profile.current_month_tokens
            : 0;

          await SupabaseAdminService.upsertUserProfile({
            ...profile,
            current_month_tokens: currentTokens + tokensUsed,
            current_month: currentMonth,
            updated_at: new Date().toISOString()
          });

        }
      }

      // Log de la acción
      await WebhookLogger.logFeatureAccess(
        userId,
        action,
        true,
        'system',
        tokensUsed,
        `Action completed successfully`
      );

    } catch (error) {
      console.error('Error recording usage:', error);
    }
  }

  /**
   * Registrar uso después de una acción exitosa (versión cliente - NO usa supabaseAdmin)
   */
  static async recordClientUsage(
    action: string,
    tokensUsed: number
  ): Promise<void> {
    try {
      const supabase = createClient();

      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        console.warn('Cannot record usage: user not authenticated');
        return;
      }

      if (tokensUsed > 0) {
        // Obtener perfil actual usando cliente normal
        const { data: profile, error: profileError } = await supabase
          .from('user_profiles')
          .select('subscription_plan, monthly_token_limit, current_month_tokens, current_month')
          .eq('user_id', user.id)
          .single();

        if (profileError) {
          console.error('Error fetching profile for usage recording:', profileError);
          return;
        }

        if (profile) {
          const currentMonth = new Date().toISOString().slice(0, 7) + '-01';
          const currentTokens = profile.current_month === currentMonth
            ? profile.current_month_tokens
            : 0;

          // Actualizar tokens usando cliente normal
          const { error: updateError } = await supabase
            .from('user_profiles')
            .update({
              current_month_tokens: currentTokens + tokensUsed,
              current_month: currentMonth,
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id);

          if (updateError) {
            console.error('Error updating token usage:', updateError);
          } else {

          }
        }
      }

      // Log de la acción (WebhookLogger ya maneja el contexto cliente/servidor)
      await WebhookLogger.logFeatureAccess(
        user.id,
        action,
        true,
        'system',
        tokensUsed,
        `Action completed successfully`
      );

    } catch (error) {
      console.error('Error recording client usage:', error);
    }
  }
}
