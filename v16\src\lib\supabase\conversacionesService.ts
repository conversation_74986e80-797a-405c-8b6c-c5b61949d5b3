import { supabase, Conversacion, Mensaje } from './supabaseClient';

/**
 * Crea una nueva conversación
 * @param titulo Título de la conversación
 * @param activa Si la conversación debe marcarse como activa
 */
export async function crearConversacion(titulo: string, activa: boolean = false): Promise<string | null> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para crear conversación');
      return null;
    }

    // Si la conversación va a ser activa, primero desactivamos todas las demás
    if (activa) {
      await desactivarTodasLasConversaciones();
    }

    const { data, error } = await supabase
      .from('conversaciones')
      .insert([{
        titulo,
        activa,
        user_id: user.id
      }])
      .select();

    if (error) {
      console.error('Error al crear conversación:', error);
      return null;
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error inesperado al crear conversación:', error);
    return null;
  }
}

/**
 * Obtiene todas las conversaciones ordenadas por fecha de actualización
 */
export async function obtenerConversaciones(): Promise<Conversacion[]> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para obtener conversaciones');
      return [];
    }

    const { data, error } = await supabase
      .from('conversaciones')
      .select('*')
      .eq('user_id', user.id)
      .order('actualizado_en', { ascending: false });

    if (error) {
      console.error('Error al obtener conversaciones:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error inesperado al obtener conversaciones:', error);
    return [];
  }
}

/**
 * Obtiene una conversación específica por su ID
 */
export async function obtenerConversacionPorId(id: string): Promise<Conversacion | null> {
  const { data, error } = await supabase
    .from('conversaciones')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error al obtener conversación:', error);
    return null;
  }

  return data;
}

/**
 * Actualiza el título de una conversación
 */
export async function actualizarConversacion(id: string, titulo: string): Promise<boolean> {
  const { error } = await supabase
    .from('conversaciones')
    .update({ titulo, actualizado_en: new Date().toISOString() })
    .eq('id', id);

  if (error) {
    console.error('Error al actualizar conversación:', error);
    return false;
  }

  return true;
}

/**
 * Marca una conversación como activa y desactiva todas las demás
 */
export async function activarConversacion(id: string): Promise<boolean> {
  try {
    // Primero desactivamos todas las conversaciones
    await desactivarTodasLasConversaciones();

    // Luego activamos la conversación específica
    const { error } = await supabase
      .from('conversaciones')
      .update({ activa: true, actualizado_en: new Date().toISOString() })
      .eq('id', id);

    if (error) {
      console.error('Error al activar conversación:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error inesperado al activar conversación:', error);
    return false;
  }
}

/**
 * Desactiva todas las conversaciones
 */
export async function desactivarTodasLasConversaciones(): Promise<boolean> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para desactivar conversaciones');
      return false;
    }

    // Desactivar todas las conversaciones del usuario actual
    const { error } = await supabase
      .from('conversaciones')
      .update({ activa: false })
      .eq('user_id', user.id)
      .eq('activa', true); // Solo actualizar las que están activas

    if (error) {
      console.error('Error al desactivar todas las conversaciones:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error inesperado al desactivar conversaciones:', error);
    return false;
  }
}

/**
 * Obtiene la conversación activa actual
 */
export async function obtenerConversacionActiva(): Promise<Conversacion | null> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.warn('No hay usuario autenticado para obtener conversación activa');
      return null;
    }

    // Primero intentar obtener todas las conversaciones del usuario para debug
    const { data: todasConversaciones, error: errorTodas } = await supabase
      .from('conversaciones')
      .select('*')
      .eq('user_id', user.id);

    if (errorTodas) {
      console.error('Error al obtener todas las conversaciones:', errorTodas);
    }

    // Ahora intentar obtener solo las activas
    const { data, error } = await supabase
      .from('conversaciones')
      .select('*')
      .eq('user_id', user.id)
      .eq('activa', true)
      .limit(1);

    if (error) {
      // Si es un error 406, puede ser que no haya conversaciones activas
      if (error.code === '406' || error.message.includes('406')) {
        return null;
      }
      console.error('Error al obtener conversación activa:', error);
      return null;
    }

    // Si hay datos, devolver el primer elemento, si no, devolver null
    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error('Error inesperado al obtener conversación activa:', error);
    return null;
  }
}

/**
 * Guarda un mensaje en la base de datos
 */
export async function guardarMensaje(mensaje: Omit<Mensaje, 'id' | 'timestamp'>): Promise<string | null> {
  try {
    // Primero verificamos que la conversación exista
    const { data: conversacion, error: errorConversacion } = await supabase
      .from('conversaciones')
      .select('id')
      .eq('id', mensaje.conversacion_id)
      .single();

    if (errorConversacion) {
      console.error('Error al verificar la conversación:', errorConversacion);
      return null;
    }

    // Guardar el mensaje
    const { data, error } = await supabase
      .from('mensajes')
      .insert([mensaje])
      .select();

    if (error) {
      console.error('Error al guardar mensaje:', error);
      return null;
    }

    // Actualizar la fecha de la conversación
    const { error: errorActualizacion } = await supabase
      .from('conversaciones')
      .update({ actualizado_en: new Date().toISOString() })
      .eq('id', mensaje.conversacion_id);

    if (errorActualizacion) {
      console.error('Error al actualizar la fecha de la conversación:', errorActualizacion);
      // No retornamos error aquí porque el mensaje ya se guardó correctamente
    }

    return data?.[0]?.id || null;
  } catch (error) {
    console.error('Error inesperado al guardar mensaje:', error);
    return null;
  }
}

/**
 * Obtiene todos los mensajes de una conversación
 */
export async function obtenerMensajesPorConversacionId(conversacionId: string): Promise<Mensaje[]> {
  const { data, error } = await supabase
    .from('mensajes')
    .select('*')
    .eq('conversacion_id', conversacionId)
    .order('timestamp', { ascending: true });

  if (error) {
    console.error('Error al obtener mensajes:', error);
    return [];
  }

  return data || [];
}

/**
 * Elimina una conversación y todos sus mensajes asociados
 */
export async function eliminarConversacion(conversacionId: string): Promise<boolean> {
  try {
    // Obtener el usuario actual
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      console.error('No hay usuario autenticado para eliminar conversación');
      return false;
    }

    // Primero eliminar todos los mensajes de la conversación
    const { error: errorMensajes } = await supabase
      .from('mensajes')
      .delete()
      .eq('conversacion_id', conversacionId);

    if (errorMensajes) {
      console.error('Error al eliminar mensajes de la conversación:', errorMensajes);
      return false;
    }

    // Luego eliminar la conversación
    const { error: errorConversacion, count } = await supabase
      .from('conversaciones')
      .delete({ count: 'exact' })
      .eq('id', conversacionId)
      .eq('user_id', user.id); // Asegurar que solo se eliminen conversaciones del usuario actual

    if (errorConversacion) {
      console.error('Error al eliminar conversación:', errorConversacion);
      return false;
    }

    if (count === 0) {
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error inesperado al eliminar conversación:', error);
    return false;
  }
}
