# Convenciones de Código - OposiAI

Este documento establece las convenciones de código para mantener consistencia y calidad en el proyecto OposiAI.

## 📋 Tabla de Contenidos

- [Nomenclatura](#nomenclatura)
- [Estructura de Archivos](#estructura-de-archivos)
- [TypeScript](#typescript)
- [React](#react)
- [Estilos](#estilos)
- [Testing](#testing)
- [Comentarios y Documentación](#comentarios-y-documentación)

## 🏷️ Nomenclatura

### Archivos y Directorios

```
✅ Correcto
components/UserProfile.tsx
hooks/useAuth.ts
services/authService.ts
types/user.types.ts
utils/formatDate.ts

❌ Incorrecto
components/userprofile.tsx
hooks/UseAuth.ts
services/auth_service.ts
types/UserTypes.ts
utils/format-date.ts
```

**Reglas:**
- **Componentes**: PascalCase (`UserProfile.tsx`)
- **Hooks**: camelCase con prefijo `use` (`useAuth.ts`)
- **Servicios**: camelCase con sufijo `Service` (`authService.ts`)
- **Tipos**: camelCase con sufijo `.types` (`user.types.ts`)
- **Utilidades**: camelCase (`formatDate.ts`)
- **Directorios**: kebab-case (`study-plan/`)

### Variables y Funciones

```typescript
// ✅ Variables: camelCase
const userName = 'john_doe';
const isAuthenticated = true;
const userProfileData = {};

// ✅ Funciones: camelCase
function getUserProfile() {}
const handleSubmit = () => {};
const validateEmail = (email: string) => {};

// ✅ Constantes: UPPER_SNAKE_CASE
const API_ENDPOINTS = {
  USERS: '/api/users',
  DOCUMENTS: '/api/documents'
};

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// ✅ Tipos e Interfaces: PascalCase
interface UserProfile {
  id: string;
  email: string;
}

type AuthState = 'loading' | 'authenticated' | 'unauthenticated';

// ❌ Evitar
const user_name = 'john'; // snake_case
const IsAuthenticated = true; // PascalCase para variables
const api_endpoints = {}; // snake_case para constantes
```

### Componentes React

```typescript
// ✅ Componente con Props Interface
interface UserCardProps {
  user: UserProfile;
  onEdit?: (user: UserProfile) => void;
  className?: string;
}

export const UserCard: React.FC<UserCardProps> = ({ 
  user, 
  onEdit, 
  className 
}) => {
  return <div className={className}>{/* JSX */}</div>;
};

// ✅ Export por defecto cuando es el componente principal del archivo
export default UserCard;
```

## 📁 Estructura de Archivos

### Organización por Features

```
src/features/auth/
├── components/
│   ├── LoginForm.tsx
│   ├── RegisterForm.tsx
│   ├── PasswordResetForm.tsx
│   └── index.ts              # Re-exports
├── hooks/
│   ├── useAuth.ts
│   ├── useAuthRedirect.ts
│   └── index.ts
├── services/
│   ├── authService.ts
│   └── index.ts
├── types/
│   ├── auth.types.ts
│   └── index.ts
└── __tests__/
    ├── components/
    ├── hooks/
    └── services/
```

### Index Files para Re-exports

```typescript
// src/features/auth/components/index.ts
export { LoginForm } from './LoginForm';
export { RegisterForm } from './RegisterForm';
export { PasswordResetForm } from './PasswordResetForm';

// src/features/auth/index.ts
export * from './components';
export * from './hooks';
export * from './services';
export * from './types';
```

### Imports Organizados

```typescript
// ✅ Orden de imports
// 1. React y librerías externas
import React, { useState, useEffect } from 'react';
import { NextPage } from 'next';
import { toast } from 'react-hot-toast';

// 2. Imports internos (absolutos)
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/features/auth';
import { formatDate } from '@/lib/utils/dateUtils';

// 3. Imports relativos
import { UserCard } from './UserCard';
import { validateForm } from '../utils/validation';

// 4. Tipos (al final)
import type { UserProfile } from '@/types/user.types';
import type { ComponentProps } from './types';
```

## 🔷 TypeScript

### Definición de Tipos

```typescript
// ✅ Interfaces para objetos
interface UserProfile {
  readonly id: string;
  email: string;
  name: string;
  createdAt: Date;
  settings?: UserSettings;
}

// ✅ Types para uniones y primitivos
type AuthState = 'idle' | 'loading' | 'authenticated' | 'error';
type UserId = string;

// ✅ Generics cuando sea necesario
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// ✅ Utility types
type CreateUserDto = Omit<UserProfile, 'id' | 'createdAt'>;
type UpdateUserDto = Partial<Pick<UserProfile, 'name' | 'email'>>;
```

### Funciones Tipadas

```typescript
// ✅ Funciones con tipos explícitos
async function fetchUserProfile(userId: string): Promise<UserProfile | null> {
  try {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    return null;
  }
}

// ✅ Arrow functions tipadas
const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// ✅ Event handlers tipados
const handleSubmit = (event: React.FormEvent<HTMLFormElement>): void => {
  event.preventDefault();
  // Handle form submission
};
```

### Evitar `any`

```typescript
// ❌ Evitar any
const processData = (data: any) => {
  return data.map((item: any) => item.value);
};

// ✅ Usar tipos específicos
interface DataItem {
  value: string;
  id: number;
}

const processData = (data: DataItem[]): string[] => {
  return data.map(item => item.value);
};

// ✅ Usar unknown para casos donde el tipo es realmente desconocido
const parseJson = (json: string): unknown => {
  return JSON.parse(json);
};
```

## ⚛️ React

### Estructura de Componentes

```typescript
// ✅ Estructura estándar de componente
import React, { useState, useEffect, useCallback } from 'react';
import type { UserProfile } from '@/types/user.types';

interface UserProfileCardProps {
  user: UserProfile;
  onUpdate?: (user: UserProfile) => void;
  className?: string;
}

export const UserProfileCard: React.FC<UserProfileCardProps> = ({
  user,
  onUpdate,
  className = ''
}) => {
  // 1. Hooks de estado
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState(user);
  
  // 2. Hooks de efecto
  useEffect(() => {
    setFormData(user);
  }, [user]);
  
  // 3. Handlers y funciones
  const handleSave = useCallback(async () => {
    try {
      // Save logic
      onUpdate?.(formData);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save user:', error);
    }
  }, [formData, onUpdate]);
  
  const handleCancel = useCallback(() => {
    setFormData(user);
    setIsEditing(false);
  }, [user]);
  
  // 4. Early returns
  if (!user) {
    return <div>No user data available</div>;
  }
  
  // 5. Render principal
  return (
    <div className={`user-profile-card ${className}`}>
      {isEditing ? (
        <EditForm 
          data={formData}
          onChange={setFormData}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      ) : (
        <DisplayView 
          user={user}
          onEdit={() => setIsEditing(true)}
        />
      )}
    </div>
  );
};

export default UserProfileCard;
```

### Hooks Personalizados

```typescript
// ✅ Hook bien estructurado
import { useState, useEffect, useCallback } from 'react';
import { userService } from '@/lib/services/userService';
import type { UserProfile } from '@/types/user.types';

interface UseUserReturn {
  user: UserProfile | null;
  loading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  updateUser: (data: Partial<UserProfile>) => Promise<void>;
}

export const useUser = (userId: string): UseUserReturn => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  const fetchUser = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await userService.getById(userId);
      setUser(userData);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [userId]);
  
  const updateUser = useCallback(async (data: Partial<UserProfile>) => {
    try {
      const updatedUser = await userService.update(userId, data);
      setUser(updatedUser);
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [userId]);
  
  useEffect(() => {
    fetchUser();
  }, [fetchUser]);
  
  return {
    user,
    loading,
    error,
    refetch: fetchUser,
    updateUser
  };
};
```

### Conditional Rendering

```typescript
// ✅ Conditional rendering claro
const UserDashboard: React.FC = () => {
  const { user, loading, error } = useAuth();
  
  // Early returns para casos especiales
  if (loading) {
    return <LoadingSpinner />;
  }
  
  if (error) {
    return <ErrorMessage error={error} />;
  }
  
  if (!user) {
    return <LoginPrompt />;
  }
  
  // Render principal
  return (
    <div className="dashboard">
      <Header user={user} />
      <main>
        {user.isPremium ? (
          <PremiumFeatures />
        ) : (
          <FreeFeatures />
        )}
      </main>
    </div>
  );
};
```

## 🎨 Estilos

### Tailwind CSS

```typescript
// ✅ Clases organizadas y legibles
const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  size = 'md',
  children,
  className = '',
  ...props 
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`;
  
  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
};
```

### CSS Modules (cuando sea necesario)

```css
/* UserCard.module.css */
.card {
  @apply bg-white rounded-lg shadow-md p-6;
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  @apply shadow-lg;
  transform: translateY(-2px);
}

.cardTitle {
  @apply text-xl font-semibold text-gray-900 mb-2;
}

.cardContent {
  @apply text-gray-600;
}
```

## 🧪 Testing

### Nomenclatura de Tests

```typescript
// ✅ Nombres descriptivos
describe('UserProfileCard', () => {
  describe('when user is provided', () => {
    it('should display user information', () => {});
    it('should enter edit mode when edit button is clicked', () => {});
    it('should save changes when save button is clicked', () => {});
  });
  
  describe('when user is null', () => {
    it('should display no data message', () => {});
  });
  
  describe('error handling', () => {
    it('should display error message when save fails', () => {});
  });
});
```

### Estructura de Tests

```typescript
// ✅ Test bien estructurado
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserProfileCard } from './UserProfileCard';
import { mockUser } from '@/__tests__/setup/testUtils';

describe('UserProfileCard', () => {
  const defaultProps = {
    user: mockUser,
    onUpdate: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should display user information', () => {
    render(<UserProfileCard {...defaultProps} />);
    
    expect(screen.getByText(mockUser.name)).toBeInTheDocument();
    expect(screen.getByText(mockUser.email)).toBeInTheDocument();
  });

  it('should handle edit mode', async () => {
    const user = userEvent.setup();
    render(<UserProfileCard {...defaultProps} />);
    
    await user.click(screen.getByRole('button', { name: /edit/i }));
    
    expect(screen.getByRole('textbox', { name: /name/i })).toBeInTheDocument();
  });
});
```

## 📝 Comentarios y Documentación

### JSDoc para Funciones Públicas

```typescript
/**
 * Valida el acceso de un usuario a una funcionalidad específica
 * @param userId - ID único del usuario
 * @param feature - Nombre de la funcionalidad a validar
 * @param tokenCost - Costo en tokens de la operación
 * @returns Promise que resuelve con el resultado de la validación
 * @throws {Error} Cuando el usuario no tiene permisos suficientes
 * @example
 * ```typescript
 * const result = await validateFeatureAccess('user-123', 'ai_chat', 1000);
 * if (result.allowed) {
 *   // Proceder con la operación
 * }
 * ```
 */
export async function validateFeatureAccess(
  userId: string,
  feature: string,
  tokenCost: number
): Promise<ValidationResult> {
  // Implementation
}
```

### Comentarios en Código

```typescript
// ✅ Comentarios útiles que explican el "por qué"
const calculateTokenCost = (text: string): number => {
  // OpenAI cuenta aproximadamente 4 caracteres por token
  // Añadimos un 20% de margen para ser conservadores
  const baseTokens = Math.ceil(text.length / 4);
  return Math.ceil(baseTokens * 1.2);
};

// ✅ Comentarios para lógica compleja
const processPaymentWebhook = async (event: StripeEvent) => {
  // Verificar que el evento no haya sido procesado previamente
  // para evitar duplicados en caso de reintentos de webhook
  const existingTransaction = await getTransactionByEventId(event.id);
  if (existingTransaction) {
    return { success: true, message: 'Event already processed' };
  }
  
  // Procesar según el tipo de evento
  switch (event.type) {
    case 'checkout.session.completed':
      return handleCheckoutCompleted(event.data.object);
    default:
      // Log eventos no manejados para debugging
       (`Unhandled event type: ${event.type}`);
      return { success: true, message: 'Event type not handled' };
  }
};

// ❌ Evitar comentarios obvios
const user = getUser(); // Get the user
const isValid = validateEmail(email); // Validate email
```

### TODO y FIXME

```typescript
// ✅ TODOs específicos con contexto
// TODO: Implementar cache de Redis para mejorar performance
// Ticket: #123 - Fecha estimada: 2025-02-15
const getUserProfile = async (userId: string) => {
  return await database.users.findById(userId);
};

// ✅ FIXME con explicación
// FIXME: Este cálculo no considera tokens de imágenes
// Necesita actualización cuando se implemente soporte para imágenes
const calculateTokens = (content: string) => {
  return content.length / 4;
};
```

---

Estas convenciones ayudan a mantener un código consistente, legible y mantenible en todo el proyecto OposiAI. 🚀
