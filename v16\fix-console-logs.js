const fs = require('fs');
const path = require('path');

// Función para arreglar un archivo específicamente
function fixFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // SOLO eliminar líneas que empiecen con espacios y tengan EXACTAMENTE el patrón problemático
    // Patrón: líneas que empiezan con espacios y solo tienen ('texto'); o ('texto',
    const lines = content.split('\n');
    const fixedLines = lines.filter(line => {
      // Eliminar líneas que son SOLO espacios + ('texto'); o ('texto',
      const trimmed = line.trim();
      
      // Patrón específico: línea que empieza con ( y termina con ); y contiene solo texto entre comillas
      if (trimmed.match(/^\(\s*['"`].*?['"`]\s*\);?\s*$/)) {
        return false; // Eliminar esta línea
      }
      
      // Patrón específico: línea que empieza con ( y contiene texto entre comillas seguido de coma
      if (trimmed.match(/^\(\s*['"`].*?['"`].*?,/)) {
        return false; // Eliminar esta línea
      }
      
      return true; // Mantener la línea
    });
    
    content = fixedLines.join('\n');
    
    // Solo escribir si hay cambios
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Arreglado: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error arreglando ${filePath}:`, error.message);
    return false;
  }
}

// Función para buscar archivos recursivamente
function findFiles(dir, extensions = ['.ts', '.tsx']) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files = files.concat(findFiles(fullPath, extensions));
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error leyendo directorio ${dir}:`, error.message);
  }
  
  return files;
}

// Ejecutar arreglo
console.log('🔧 Iniciando arreglo específico de console.log...');

const srcDir = path.join(__dirname, 'src');
const files = findFiles(srcDir);

console.log(`📁 Encontrados ${files.length} archivos TypeScript/TSX`);

let fixedCount = 0;
for (const file of files) {
  if (fixFile(file)) {
    fixedCount++;
  }
}

console.log(`\n✨ Arreglo completado:`);
console.log(`   - Archivos procesados: ${files.length}`);
console.log(`   - Archivos modificados: ${fixedCount}`);
console.log(`   - Archivos sin cambios: ${files.length - fixedCount}`);
