"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPlus,FiPrinter,FiRefreshCw,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/config/plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n/* harmony import */ var _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/hooks/useUserPlan */ \"(app-pages-browser)/./src/hooks/useUserPlan.ts\");\n// ===== Archivo: src\\app\\app\\page.tsx (Corregido) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // <-- MODIFICACIÓN 1\n // <-- MODIFICACIÓN 2: Importación corregida\n\n\n\n\n\n\nfunction AppPage() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTokenStats, setShowTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshSummaries, setRefreshSummaries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStudyPlanningAccess, setHasStudyPlanningAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasAiTutorAccess, setHasAiTutorAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSummaryAccess, setHasSummaryAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shouldRefreshTokenStats, setShouldRefreshTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plan: userPlan, isLoading: planLoading } = (0,_hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan)();\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration)();\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults)({\n        onResult: {\n            \"AppPage.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"AppPage.usePlanEstudiosResults\"],\n        onError: {\n            \"AppPage.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"AppPage.usePlanEstudiosResults\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            console.log('🔄 useEffect ejecutándose con:', {\n                isLoading,\n                planLoading,\n                user: user === null || user === void 0 ? void 0 : user.id,\n                userPlan,\n                timestamp: new Date().toISOString()\n            });\n            if (isLoading || planLoading || !user) {\n                console.log('⏳ Esperando datos:', {\n                    isLoading,\n                    planLoading,\n                    hasUser: !!user\n                });\n                return;\n            }\n            const checkInitialSetup = {\n                \"AppPage.useEffect.checkInitialSetup\": async ()=>{\n                    try {\n                        console.log('🔍 Verificando setup inicial:', {\n                            userPlan,\n                            userPlanType: typeof userPlan,\n                            userId: user === null || user === void 0 ? void 0 : user.id,\n                            timestamp: new Date().toISOString()\n                        });\n                        // Verificar si el usuario ya tiene un temario configurado\n                        const temarioConfigurado = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.tieneTemarioConfigurado)();\n                        console.log('📋 Temario configurado:', temarioConfigurado);\n                        // LÓGICA CLAVE: Solo mostrar setup para plan 'pro' sin temario\n                        console.log('🎯 Evaluando condición:', {\n                            userPlan,\n                            esPro: userPlan === 'pro',\n                            temarioConfigurado,\n                            condicionCompleta: userPlan === 'pro' && !temarioConfigurado\n                        });\n                        if (userPlan === 'pro' && !temarioConfigurado) {\n                            console.log('✅ Mostrando setup de temario para usuario pro sin temario');\n                            setMostrarSetupTemario(true);\n                        } else {\n                            console.log('🏠 Redirigiendo al dashboard:', {\n                                plan: userPlan,\n                                tieneTemario: temarioConfigurado,\n                                razon: userPlan !== 'pro' ? 'Plan no es pro' : 'Ya tiene temario configurado'\n                            });\n                            setMostrarSetupTemario(false);\n                            cargarDatosDashboard();\n                        }\n                    } catch (error) {\n                        console.error('❌ Error en checkInitialSetup:', error);\n                        // En caso de error, ir al dashboard por defecto (comportamiento seguro)\n                        console.log('🔄 Fallback: Redirigiendo al dashboard por error');\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    }\n                }\n            }[\"AppPage.useEffect.checkInitialSetup\"];\n            checkInitialSetup();\n            checkAccessFeatures();\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        userPlan,\n        planLoading\n    ]);\n    const cargarDatosDashboard = async ()=>{\n        if (!user) return;\n        try {\n            const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.obtenerTemarioUsuario)();\n            if (temario) {\n                setTemarioId(temario.id);\n                const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__.tienePlanificacionConfigurada)(temario.id);\n                setTienePlanificacion(tienePlan);\n                const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                if (planExistente === null || planExistente === void 0 ? void 0 : planExistente.plan_data) {\n                    setPlanEstudios(planExistente.plan_data);\n                } else {\n                    setPlanEstudios(null);\n                }\n            } else {\n                setTemarioId(null);\n                setTienePlanificacion(false);\n                setPlanEstudios(null);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        }\n    };\n    const checkAccessFeatures = async ()=>{\n        if (user) {\n            const [studyPlanningAccess, aiTutorAccess, summaryAccess] = await Promise.all([\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('study_planning'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('ai_tutor_chat'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('summary_a1_a2')\n            ]);\n            setHasStudyPlanningAccess(studyPlanningAccess);\n            setHasAiTutorAccess(aiTutorAccess);\n            setHasSummaryAccess(summaryAccess);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    if (isLoading || planLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 184,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        cargarDatosDashboard();\n    };\n    if (mostrarSetupTemario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 196,\n            columnNumber: 12\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>setPlanEstudios(result),\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const handleNavigateToTab = (tab)=>{\n        setActiveTab(tab);\n    };\n    const handleSummaryGenerated = (summaryId)=>{\n        setRefreshSummaries((prev)=>prev + 1);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center -ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo2.png\",\n                                        alt: \"OposiAI Logo\",\n                                        width: 80,\n                                        height: 80,\n                                        className: \"h-20 w-20 object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/profile'),\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTokenStats(true),\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        title: \"Ver estad\\xedsticas de uso de IA\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiBarChart, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPlus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nuevo documento\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 351,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 410,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 420,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiFileText, {\n                                        className: \"w-4 h-4 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 437,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-base font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 436,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 498,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 510,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 517,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 513,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 538,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 537,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 551,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 546,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 536,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 582,\n                                                columnNumber: 52\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 584,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 588,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 599,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 407,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 625,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 619,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 618,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 617,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 636,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 637,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: ()=>{\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false);\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n        lineNumber: 349,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"nDqoKpz+OWY9Y6K3b7WWUu4YOYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});