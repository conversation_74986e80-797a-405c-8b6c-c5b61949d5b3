const fs = require('fs');
const path = require('path');

// Función para limpiar un archivo
function cleanFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    
    // Patrón 1: <PERSON>íneas que empiezan con espacios y solo tienen ('texto');
    content = content.replace(/^\s*\(\s*['"`].*?['"`].*?\);?\s*$/gm, '');
    
    // Patrón 2: Líneas que empiezan con espacios y ('texto',
    content = content.replace(/^\s*\(\s*['"`].*?['"`].*?,.*?\);?\s*$/gm, '');
    
    // Patrón 3: Líneas sueltas con solo paréntesis y contenido
    content = content.replace(/^\s+\(\s*['"`].*$/gm, '');
    
    // Patrón 4: Limpiar líneas vacías múltiples
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // Solo escribir si hay cambios
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Limpiado: ${filePath}`);
      return true;
    }
    return false;
  } catch (error) {
    console.error(`❌ Error limpiando ${filePath}:`, error.message);
    return false;
  }
}

// Función para buscar archivos recursivamente
function findFiles(dir, extensions = ['.ts', '.tsx']) {
  let files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files = files.concat(findFiles(fullPath, extensions));
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error leyendo directorio ${dir}:`, error.message);
  }
  
  return files;
}

// Ejecutar limpieza
console.log('🧹 Iniciando limpieza de console.log...');

const srcDir = path.join(__dirname, 'src');
const files = findFiles(srcDir);

console.log(`📁 Encontrados ${files.length} archivos TypeScript/TSX`);

let cleanedCount = 0;
for (const file of files) {
  if (cleanFile(file)) {
    cleanedCount++;
  }
}

console.log(`\n✨ Limpieza completada:`);
console.log(`   - Archivos procesados: ${files.length}`);
console.log(`   - Archivos modificados: ${cleanedCount}`);
console.log(`   - Archivos sin cambios: ${files.length - cleanedCount}`);
