"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPlus,FiPrinter,FiRefreshCw,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/config/plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n/* harmony import */ var _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/hooks/useUserPlan */ \"(app-pages-browser)/./src/hooks/useUserPlan.ts\");\n// ===== Archivo: src\\app\\app\\page.tsx (Corregido) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // <-- MODIFICACIÓN 1\n // <-- MODIFICACIÓN 2: Importación corregida\n\n\n\n\n\n\nfunction AppPage() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTokenStats, setShowTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshSummaries, setRefreshSummaries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStudyPlanningAccess, setHasStudyPlanningAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasAiTutorAccess, setHasAiTutorAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSummaryAccess, setHasSummaryAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shouldRefreshTokenStats, setShouldRefreshTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plan: userPlan, isLoading: planLoading } = (0,_hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan)();\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration)();\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults)({\n        onResult: {\n            \"AppPage.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"AppPage.usePlanEstudiosResults\"],\n        onError: {\n            \"AppPage.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"AppPage.usePlanEstudiosResults\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (isLoading || planLoading || !user) {\n                return;\n            }\n            const checkInitialSetup = {\n                \"AppPage.useEffect.checkInitialSetup\": async ()=>{\n                    try {\n                        console.log('🔍 Verificando setup inicial:', {\n                            userPlan,\n                            userPlanType: typeof userPlan,\n                            userId: user === null || user === void 0 ? void 0 : user.id,\n                            timestamp: new Date().toISOString()\n                        });\n                        // VERIFICACIÓN DIRECTA DEL PLAN: Hacer una consulta directa para asegurar el plan correcto\n                        let planReal = userPlan;\n                        try {\n                            const response = await fetch('/api/user/plan');\n                            if (response.ok) {\n                                const data = await response.json();\n                                planReal = data.plan || 'free';\n                                console.log('📊 Plan verificado directamente:', {\n                                    planDelHook: userPlan,\n                                    planDeLaAPI: planReal,\n                                    sonIguales: userPlan === planReal\n                                });\n                            }\n                        } catch (apiError) {\n                            console.warn('⚠️ Error verificando plan directamente, usando hook:', apiError);\n                        }\n                        // Verificar si el usuario ya tiene un temario configurado\n                        const temarioConfigurado = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.tieneTemarioConfigurado)();\n                        console.log('📋 Temario configurado:', temarioConfigurado);\n                        // LÓGICA CLAVE: Solo mostrar setup para plan 'pro' sin temario\n                        console.log('🎯 Evaluando condición:', {\n                            planReal,\n                            esPro: planReal === 'pro',\n                            temarioConfigurado,\n                            condicionCompleta: planReal === 'pro' && !temarioConfigurado\n                        });\n                        if (planReal === 'pro' && !temarioConfigurado) {\n                            console.log('✅ Mostrando setup de temario para usuario pro sin temario');\n                            setMostrarSetupTemario(true);\n                        } else {\n                            console.log('🏠 Redirigiendo al dashboard:', {\n                                plan: planReal,\n                                tieneTemario: temarioConfigurado,\n                                razon: planReal !== 'pro' ? 'Plan no es pro' : 'Ya tiene temario configurado'\n                            });\n                            setMostrarSetupTemario(false);\n                            cargarDatosDashboard();\n                        }\n                    } catch (error) {\n                        console.error('❌ Error en checkInitialSetup:', error);\n                        // En caso de error, ir al dashboard por defecto (comportamiento seguro)\n                        console.log('🔄 Fallback: Redirigiendo al dashboard por error');\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    }\n                }\n            }[\"AppPage.useEffect.checkInitialSetup\"];\n            checkInitialSetup();\n            checkAccessFeatures();\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        userPlan,\n        planLoading\n    ]);\n    const cargarDatosDashboard = async ()=>{\n        if (!user) return;\n        try {\n            const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.obtenerTemarioUsuario)();\n            if (temario) {\n                setTemarioId(temario.id);\n                const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__.tienePlanificacionConfigurada)(temario.id);\n                setTienePlanificacion(tienePlan);\n                const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                if (planExistente === null || planExistente === void 0 ? void 0 : planExistente.plan_data) {\n                    setPlanEstudios(planExistente.plan_data);\n                } else {\n                    setPlanEstudios(null);\n                }\n            } else {\n                setTemarioId(null);\n                setTienePlanificacion(false);\n                setPlanEstudios(null);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        }\n    };\n    const checkAccessFeatures = async ()=>{\n        if (user) {\n            const [studyPlanningAccess, aiTutorAccess, summaryAccess] = await Promise.all([\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('study_planning'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('ai_tutor_chat'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('summary_a1_a2')\n            ]);\n            setHasStudyPlanningAccess(studyPlanningAccess);\n            setHasAiTutorAccess(aiTutorAccess);\n            setHasSummaryAccess(summaryAccess);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    if (isLoading || planLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 190,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this);\n    }\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        cargarDatosDashboard();\n    };\n    if (mostrarSetupTemario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 204,\n            columnNumber: 12\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>setPlanEstudios(result),\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const handleNavigateToTab = (tab)=>{\n        setActiveTab(tab);\n    };\n    const handleSummaryGenerated = (summaryId)=>{\n        setRefreshSummaries((prev)=>prev + 1);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center -ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo2.png\",\n                                        alt: \"OposiAI Logo\",\n                                        width: 80,\n                                        height: 80,\n                                        className: \"h-20 w-20 object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/profile'),\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTokenStats(true),\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        title: \"Ver estad\\xedsticas de uso de IA\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiBarChart, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPlus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nuevo documento\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 396,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 431,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 426,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiFileText, {\n                                        className: \"w-4 h-4 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-base font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 511,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 506,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 518,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 514,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 525,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 521,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 501,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 559,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 52\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 591,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 474,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 636,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 637,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 627,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 626,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 625,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: ()=>{\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false);\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 646,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"nDqoKpz+OWY9Y6K3b7WWUu4YOYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});