"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx":
/*!*********************************************************!*\
  !*** ./src/features/dashboard/components/Dashboard.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiBookOpen,FiCalendar,FiCheckSquare,FiClock,FiFileText,FiLayers,FiPlay,FiPlus,FiTarget,FiTrendingUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/dashboardService */ \"(app-pages-browser)/./src/lib/supabase/dashboardService.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Dashboard = (param)=>{\n    let { onNavigateToTab } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proximasFlashcards, setProximasFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const [statsData, proximasData] = await Promise.all([\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasDashboard)(),\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerProximasFlashcards)(5)\n            ]);\n            setEstadisticas(statsData);\n            setProximasFlashcards(proximasData);\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n            // En caso de error, usar datos por defecto\n            setEstadisticas({\n                totalDocumentos: 0,\n                totalColeccionesFlashcards: 0,\n                totalTests: 0,\n                totalFlashcards: 0,\n                flashcardsParaHoy: 0,\n                flashcardsNuevas: 0,\n                flashcardsAprendiendo: 0,\n                flashcardsRepasando: 0,\n                testsRealizados: 0,\n                porcentajeAcierto: 0,\n                coleccionesRecientes: [],\n                testsRecientes: []\n            });\n            setProximasFlashcards([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const obtenerSaludo = ()=>{\n        const hora = new Date().getHours();\n        if (hora < 12) return 'Buenos días';\n        if (hora < 18) return 'Buenas tardes';\n        return 'Buenas noches';\n    };\n    const obtenerNombreUsuario = ()=>{\n        var _user_email;\n        return (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || 'Estudiante';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            obtenerSaludo(),\n                            \", \",\n                            obtenerNombreUsuario(),\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"\\xbfListo para continuar con tu preparaci\\xf3n? Aqu\\xed tienes un resumen de tu progreso.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Documentos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalDocumentos) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalColeccionesFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBook, {\n                                    className: \"h-8 w-8 text-emerald-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Tests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalTests) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheckSquare, {\n                                    className: \"h-8 w-8 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {\n                                    className: \"h-8 w-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Estudio de Hoy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                className: \"h-6 w-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 rounded-lg p-4 border border-orange-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-800\",\n                                                    children: \"Para Repasar Hoy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsParaHoy) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiClock, {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Nuevas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsNuevas) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBookOpen, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"% Acierto Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.porcentajeAcierto.toFixed(1)) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrendingUp, {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && estadisticas.flashcardsParaHoy > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onNavigateToTab('misFlashcards'),\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlay, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Comenzar Estudio\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            proximasFlashcards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Pr\\xf3ximas Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: proximasFlashcards.slice(0, 3).map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 truncate\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: flashcard.coleccionTitulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                            children: flashcard.estado\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, flashcard.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    proximasFlashcards.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigateToTab('misFlashcards'),\n                        className: \"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                        children: \"Ver todas las flashcards pendientes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Colecciones Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.coleccionesRecientes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: coleccion.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creada: \",\n                                                            new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        coleccion.paraHoy,\n                                                        \" para hoy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, coleccion.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misFlashcards'),\n                                className: \"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium\",\n                                children: \"Ver todas las colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Tests Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.testsRecientes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: test.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creado: \",\n                                                            new Date(test.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800\",\n                                                    children: [\n                                                        test.numeroPreguntas,\n                                                        \" preguntas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, test.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misTests'),\n                                className: \"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium\",\n                                children: \"Ver todos los tests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Acciones R\\xe1pidas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 293,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('preguntas'),\n                                className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                        className: \"h-6 w-6 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900\",\n                                        children: \"Hacer Preguntas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('flashcards'),\n                                className: \"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {\n                                        className: \"h-6 w-6 text-orange-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-orange-900\",\n                                        children: \"Crear Flashcards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('tests'),\n                                className: \"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheckSquare, {\n                                        className: \"h-6 w-6 text-indigo-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-indigo-900\",\n                                        children: \"Generar Tests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('mapas'),\n                                className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLayers, {\n                                        className: \"h-6 w-6 text-purple-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-purple-900\",\n                                        children: \"Mapas Mentales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('planEstudios'),\n                                className: \"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                        className: \"h-6 w-6 text-teal-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-teal-900\",\n                                        children: \"Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 292,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"aHdaFWEU/R61Mg+sun4xSp3R2oQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\n"));

/***/ })

});