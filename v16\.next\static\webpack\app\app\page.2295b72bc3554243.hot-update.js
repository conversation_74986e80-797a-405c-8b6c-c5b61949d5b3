"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPlus,FiPrinter,FiRefreshCw,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/config/plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n/* harmony import */ var _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/hooks/useUserPlan */ \"(app-pages-browser)/./src/hooks/useUserPlan.ts\");\n// ===== Archivo: src\\app\\app\\page.tsx (Corregido) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // <-- MODIFICACIÓN 1\n // <-- MODIFICACIÓN 2: Importación corregida\n\n\n\n\n\n\nfunction AppPage() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTokenStats, setShowTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshSummaries, setRefreshSummaries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStudyPlanningAccess, setHasStudyPlanningAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasAiTutorAccess, setHasAiTutorAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSummaryAccess, setHasSummaryAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shouldRefreshTokenStats, setShouldRefreshTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plan: userPlan, isLoading: planLoading } = (0,_hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan)();\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration)();\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults)({\n        onResult: {\n            \"AppPage.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"AppPage.usePlanEstudiosResults\"],\n        onError: {\n            \"AppPage.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"AppPage.usePlanEstudiosResults\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            console.log('🔄 useEffect ejecutándose con:', {\n                isLoading,\n                planLoading,\n                user: user === null || user === void 0 ? void 0 : user.id,\n                userPlan,\n                timestamp: new Date().toISOString()\n            });\n            if (isLoading || planLoading || !user) {\n                console.log('⏳ Esperando datos:', {\n                    isLoading,\n                    planLoading,\n                    hasUser: !!user\n                });\n                return;\n            }\n            const checkInitialSetup = {\n                \"AppPage.useEffect.checkInitialSetup\": async ()=>{\n                    try {\n                        console.log('🔍 Verificando setup inicial:', {\n                            userPlan,\n                            userPlanType: typeof userPlan,\n                            userId: user === null || user === void 0 ? void 0 : user.id,\n                            timestamp: new Date().toISOString()\n                        });\n                        // VERIFICACIÓN DIRECTA DEL PLAN: Hacer una consulta directa para asegurar el plan correcto\n                        let planReal = userPlan;\n                        try {\n                            const response = await fetch('/api/user/plan');\n                            if (response.ok) {\n                                const data = await response.json();\n                                planReal = data.plan || 'free';\n                                console.log('📊 Plan verificado directamente:', {\n                                    planDelHook: userPlan,\n                                    planDeLaAPI: planReal,\n                                    sonIguales: userPlan === planReal\n                                });\n                            }\n                        } catch (apiError) {\n                            console.warn('⚠️ Error verificando plan directamente, usando hook:', apiError);\n                        }\n                        // Verificar si el usuario ya tiene un temario configurado\n                        const temarioConfigurado = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.tieneTemarioConfigurado)();\n                        console.log('📋 Temario configurado:', temarioConfigurado);\n                        // LÓGICA CLAVE: Solo mostrar setup para plan 'pro' sin temario\n                        console.log('🎯 Evaluando condición:', {\n                            planReal,\n                            esPro: planReal === 'pro',\n                            temarioConfigurado,\n                            condicionCompleta: planReal === 'pro' && !temarioConfigurado\n                        });\n                        if (planReal === 'pro' && !temarioConfigurado) {\n                            console.log('✅ Mostrando setup de temario para usuario pro sin temario');\n                            console.log('🔧 Estableciendo mostrarSetupTemario = true');\n                            setMostrarSetupTemario(true);\n                        } else {\n                            console.log('🏠 Redirigiendo al dashboard:', {\n                                plan: planReal,\n                                tieneTemario: temarioConfigurado,\n                                razon: planReal !== 'pro' ? 'Plan no es pro' : 'Ya tiene temario configurado'\n                            });\n                            console.log('🔧 Estableciendo mostrarSetupTemario = false');\n                            setMostrarSetupTemario(false);\n                            cargarDatosDashboard();\n                        }\n                    } catch (error) {\n                        console.error('❌ Error en checkInitialSetup:', error);\n                        // En caso de error, ir al dashboard por defecto (comportamiento seguro)\n                        console.log('🔄 Fallback: Redirigiendo al dashboard por error');\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    }\n                }\n            }[\"AppPage.useEffect.checkInitialSetup\"];\n            checkInitialSetup();\n            checkAccessFeatures();\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        userPlan,\n        planLoading\n    ]);\n    const cargarDatosDashboard = async ()=>{\n        if (!user) return;\n        try {\n            const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.obtenerTemarioUsuario)();\n            if (temario) {\n                setTemarioId(temario.id);\n                const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__.tienePlanificacionConfigurada)(temario.id);\n                setTienePlanificacion(tienePlan);\n                const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                if (planExistente === null || planExistente === void 0 ? void 0 : planExistente.plan_data) {\n                    setPlanEstudios(planExistente.plan_data);\n                } else {\n                    setPlanEstudios(null);\n                }\n            } else {\n                setTemarioId(null);\n                setTienePlanificacion(false);\n                setPlanEstudios(null);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        }\n    };\n    const checkAccessFeatures = async ()=>{\n        if (user) {\n            const [studyPlanningAccess, aiTutorAccess, summaryAccess] = await Promise.all([\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('study_planning'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('ai_tutor_chat'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('summary_a1_a2')\n            ]);\n            setHasStudyPlanningAccess(studyPlanningAccess);\n            setHasAiTutorAccess(aiTutorAccess);\n            setHasSummaryAccess(summaryAccess);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    if (isLoading || planLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 201,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 200,\n            columnNumber: 7\n        }, this);\n    }\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        cargarDatosDashboard();\n    };\n    console.log('🎭 Estado de renderizado:', {\n        mostrarSetupTemario,\n        userPlan,\n        planLoading,\n        isLoading,\n        user: user === null || user === void 0 ? void 0 : user.id\n    });\n    if (mostrarSetupTemario) {\n        console.log('🎯 Renderizando TemarioSetup');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 224,\n            columnNumber: 12\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>setPlanEstudios(result),\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const handleNavigateToTab = (tab)=>{\n        setActiveTab(tab);\n    };\n    const handleSummaryGenerated = (summaryId)=>{\n        setRefreshSummaries((prev)=>prev + 1);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center -ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo2.png\",\n                                        alt: \"OposiAI Logo\",\n                                        width: 80,\n                                        height: 80,\n                                        className: \"h-20 w-20 object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/profile'),\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTokenStats(true),\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        title: \"Ver estad\\xedsticas de uso de IA\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiBarChart, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 413,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPlus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nuevo documento\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 393,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 451,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiFileText, {\n                                        className: \"w-4 h-4 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-base font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 464,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 471,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 463,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 509,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 522,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 531,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 538,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 534,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 545,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 541,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 521,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 556,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 566,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 568,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 579,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 574,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 583,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 52\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 613,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 616,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 632,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 649,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 648,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 656,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 657,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 654,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 653,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 646,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 664,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 665,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: ()=>{\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false);\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 666,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"nDqoKpz+OWY9Y6K3b7WWUu4YOYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});