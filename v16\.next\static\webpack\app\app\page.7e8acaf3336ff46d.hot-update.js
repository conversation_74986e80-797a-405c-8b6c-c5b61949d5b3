"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx":
/*!*********************************************************!*\
  !*** ./src/features/dashboard/components/Dashboard.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiBookOpen,FiCalendar,FiCheckSquare,FiClock,FiFileText,FiLayers,FiPlay,FiPlus,FiTarget,FiTrendingUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/dashboardService */ \"(app-pages-browser)/./src/lib/supabase/dashboardService.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst Dashboard = (param)=>{\n    let { onNavigateToTab } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proximasFlashcards, setProximasFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const [statsData, proximasData] = await Promise.all([\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasDashboard)(),\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerProximasFlashcards)(5)\n            ]);\n            setEstadisticas(statsData);\n            setProximasFlashcards(proximasData);\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n            // En caso de error, usar datos por defecto\n            setEstadisticas({\n                totalDocumentos: 0,\n                totalColeccionesFlashcards: 0,\n                totalTests: 0,\n                totalFlashcards: 0,\n                flashcardsParaHoy: 0,\n                flashcardsNuevas: 0,\n                flashcardsAprendiendo: 0,\n                flashcardsRepasando: 0,\n                testsRealizados: 0,\n                porcentajeAcierto: 0,\n                coleccionesRecientes: [],\n                testsRecientes: []\n            });\n            setProximasFlashcards([]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const obtenerSaludo = ()=>{\n        const hora = new Date().getHours();\n        if (hora < 12) return 'Buenos días';\n        if (hora < 18) return 'Buenas tardes';\n        return 'Buenas noches';\n    };\n    const obtenerNombreUsuario = ()=>{\n        var _user_email;\n        return (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || 'Estudiante';\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 86,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            obtenerSaludo(),\n                            \", \",\n                            obtenerNombreUsuario(),\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"\\xbfListo para continuar con tu preparaci\\xf3n? Aqu\\xed tienes un resumen de tu progreso.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Documentos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalDocumentos) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalColeccionesFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBook, {\n                                    className: \"h-8 w-8 text-emerald-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Tests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalTests) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheckSquare, {\n                                    className: \"h-8 w-8 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTarget, {\n                                    className: \"h-8 w-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Estudio de Hoy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                className: \"h-6 w-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 rounded-lg p-4 border border-orange-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-800\",\n                                                    children: \"Para Repasar Hoy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsParaHoy) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiClock, {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Nuevas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsNuevas) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBookOpen, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"% Acierto Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.porcentajeAcierto.toFixed(1)) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiTrendingUp, {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && estadisticas.flashcardsParaHoy > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onNavigateToTab('misFlashcards'),\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlay, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Comenzar Estudio\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, undefined),\n            proximasFlashcards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Pr\\xf3ximas Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: proximasFlashcards.slice(0, 3).map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 truncate\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: flashcard.coleccionTitulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                            children: flashcard.estado\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, flashcard.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    proximasFlashcards.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigateToTab('misFlashcards'),\n                        className: \"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                        children: \"Ver todas las flashcards pendientes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 200,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Colecciones Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.coleccionesRecientes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: coleccion.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creada: \",\n                                                            new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        coleccion.paraHoy,\n                                                        \" para hoy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, coleccion.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misFlashcards'),\n                                className: \"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium\",\n                                children: \"Ver todas las colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Tests Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.testsRecientes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: test.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creado: \",\n                                                            new Date(test.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800\",\n                                                    children: [\n                                                        test.numeroPreguntas,\n                                                        \" preguntas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, test.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misTests'),\n                                className: \"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium\",\n                                children: \"Ver todos los tests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Acciones R\\xe1pidas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('preguntas'),\n                                className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiFileText, {\n                                        className: \"h-6 w-6 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900\",\n                                        children: \"Hacer Preguntas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('flashcards'),\n                                className: \"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiPlus, {\n                                        className: \"h-6 w-6 text-orange-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-orange-900\",\n                                        children: \"Crear Flashcards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('tests'),\n                                className: \"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCheckSquare, {\n                                        className: \"h-6 w-6 text-indigo-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-indigo-900\",\n                                        children: \"Generar Tests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('mapas'),\n                                className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLayers, {\n                                        className: \"h-6 w-6 text-purple-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-purple-900\",\n                                        children: \"Mapas Mentales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('planEstudios'),\n                                className: \"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiCalendar, {\n                                        className: \"h-6 w-6 text-teal-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-teal-900\",\n                                        children: \"Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 295,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"aHdaFWEU/R61Mg+sun4xSp3R2oQ=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\n"));

/***/ })

});