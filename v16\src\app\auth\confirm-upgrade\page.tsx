// src/app/auth/confirm-upgrade/page.tsx
// Página para confirmar actualizaciones de plan (SEGURIDAD)

'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

interface ConfirmationState {
  loading: boolean;
  success: boolean;
  error: string | null;
  planId?: string;
  alreadyConfirmed?: boolean;
}

export default function ConfirmUpgradePage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [state, setState] = useState<ConfirmationState>({
    loading: true,
    success: false,
    error: null
  });

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setState({
        loading: false,
        success: false,
        error: 'Token de confirmación no válido'
      });
      return;
    }

    confirmUpgrade(token);
  }, [token]);

  const confirmUpgrade = async (confirmationToken: string) => {
    try {

      const response = await fetch('/api/auth/confirm-plan-upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token: confirmationToken }),
      });

      const data = await response.json();

      if (response.ok && data.success) {

        setState({
          loading: false,
          success: true,
          error: null,
          planId: data.planId,
          alreadyConfirmed: data.alreadyConfirmed
        });

        if (!data.alreadyConfirmed) {
          toast.success('¡Plan actualizado exitosamente!');
        }
      } else {
        console.error('❌ [SECURITY] Error confirmando upgrade:', data.error);
        setState({
          loading: false,
          success: false,
          error: data.error || 'Error desconocido'
        });
        toast.error(data.error || 'Error al confirmar la actualización');
      }
    } catch (error) {
      console.error('❌ [SECURITY] Error de red confirmando upgrade:', error);
      setState({
        loading: false,
        success: false,
        error: 'Error de conexión. Por favor, inténtalo de nuevo.'
      });
      toast.error('Error de conexión');
    }
  };

  const handleGoToApp = () => {
    router.push('/app');
  };

  const handleGoToLogin = () => {
    router.push('/login');
  };

  if (state.loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                Confirmando actualización...
              </h2>
              <p className="mt-2 text-center text-sm text-gray-600">
                Por favor, espera mientras procesamos tu confirmación
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (state.success) {
    const planDisplayName = state.planId === 'usuario' ? 'Usuario' : state.planId === 'pro' ? 'Pro' : state.planId;
    
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              
              <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
                {state.alreadyConfirmed ? '✅ Ya Confirmado' : '🎉 ¡Confirmado!'}
              </h2>
              
              <p className="mt-2 text-center text-sm text-gray-600">
                {state.alreadyConfirmed 
                  ? 'Tu plan ya había sido actualizado previamente'
                  : `Tu plan ha sido actualizado exitosamente al Plan ${planDisplayName}`
                }
              </p>
              
              <div className="mt-6">
                <button
                  onClick={handleGoToApp}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Ir a la aplicación
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Estado de error
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
              <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
            
            <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
              Error de Confirmación
            </h2>
            
            <p className="mt-2 text-center text-sm text-gray-600">
              {state.error}
            </p>
            
            <div className="mt-6 space-y-3">
              <button
                onClick={() => window.location.reload()}
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Intentar de nuevo
              </button>
              
              <button
                onClick={handleGoToLogin}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Ir al inicio de sesión
              </button>
            </div>
            
            <p className="mt-4 text-center text-xs text-gray-500">
              Si el problema persiste, contacta con nuestro soporte
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
