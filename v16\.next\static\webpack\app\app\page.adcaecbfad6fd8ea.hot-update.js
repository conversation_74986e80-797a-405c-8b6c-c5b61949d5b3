"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/app/app/page.tsx":
/*!******************************!*\
  !*** ./src/app/app/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AppPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=FiBarChart,FiCalendar,FiCheck,FiDownload,FiFileText,FiLogOut,FiPlus,FiPrinter,FiRefreshCw,FiUser!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../features/documents/components/DocumentSelector */ \"(app-pages-browser)/./src/features/documents/components/DocumentSelector.tsx\");\n/* harmony import */ var _features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../features/conversations/components/QuestionForm */ \"(app-pages-browser)/./src/features/conversations/components/QuestionForm.tsx\");\n/* harmony import */ var _features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../features/documents/components/DocumentUploader */ \"(app-pages-browser)/./src/features/documents/components/DocumentUploader.tsx\");\n/* harmony import */ var _features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../features/mindmaps/components/MindMapGenerator */ \"(app-pages-browser)/./src/features/mindmaps/components/MindMapGenerator.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardGenerator */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../../features/summaries/components/SummaryGenerator */ \"(app-pages-browser)/./src/features/summaries/components/SummaryGenerator.tsx\");\n/* harmony import */ var _features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../features/summaries/components/SummaryList */ \"(app-pages-browser)/./src/features/summaries/components/SummaryList.tsx\");\n/* harmony import */ var _features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../features/documents/components/DocumentManager */ \"(app-pages-browser)/./src/features/documents/components/DocumentManager.tsx\");\n/* harmony import */ var _features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../../features/flashcards/components/FlashcardViewer */ \"(app-pages-browser)/./src/features/flashcards/components/FlashcardViewer.tsx\");\n/* harmony import */ var _features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../../features/tests/components/TestGenerator */ \"(app-pages-browser)/./src/features/tests/components/TestGenerator.tsx\");\n/* harmony import */ var _features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../../features/tests/components/TestViewer */ \"(app-pages-browser)/./src/features/tests/components/TestViewer.tsx\");\n/* harmony import */ var _features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../../features/dashboard/components/Dashboard */ \"(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\");\n/* harmony import */ var _features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../../features/temario/components/TemarioManager */ \"(app-pages-browser)/./src/features/temario/components/TemarioManager.tsx\");\n/* harmony import */ var _features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../../features/shared/components/MobileDebugInfo */ \"(app-pages-browser)/./src/features/shared/components/MobileDebugInfo.tsx\");\n/* harmony import */ var _features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../../features/shared/components/DiagnosticPanel */ \"(app-pages-browser)/./src/features/shared/components/DiagnosticPanel.tsx\");\n/* harmony import */ var _features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ../../features/shared/components/SidebarMenu */ \"(app-pages-browser)/./src/features/shared/components/SidebarMenu.tsx\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/hooks/useBackgroundGeneration */ \"(app-pages-browser)/./src/hooks/useBackgroundGeneration.ts\");\n/* harmony import */ var _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/hooks/usePlanEstudiosResults */ \"(app-pages-browser)/./src/hooks/usePlanEstudiosResults.ts\");\n/* harmony import */ var _config_plans__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/config/plans */ \"(app-pages-browser)/./src/config/plans.ts\");\n/* harmony import */ var _components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/UpgradePlanMessage */ \"(app-pages-browser)/./src/components/ui/UpgradePlanMessage.tsx\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n/* harmony import */ var _features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/features/planificacion/services/planificacionService */ \"(app-pages-browser)/./src/features/planificacion/services/planificacionService.ts\");\n/* harmony import */ var _features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/features/planificacion/services/planEstudiosClientService */ \"(app-pages-browser)/./src/features/planificacion/services/planEstudiosClientService.ts\");\n/* harmony import */ var _features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/features/planificacion/components/PlanEstudiosViewer */ \"(app-pages-browser)/./src/features/planificacion/components/PlanEstudiosViewer.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/ui/TokenStatsModal */ \"(app-pages-browser)/./src/components/ui/TokenStatsModal.tsx\");\n/* harmony import */ var _features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @/features/temario/components/TemarioSetup */ \"(app-pages-browser)/./src/features/temario/components/TemarioSetup.tsx\");\n/* harmony import */ var _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @/hooks/useUserPlan */ \"(app-pages-browser)/./src/hooks/useUserPlan.ts\");\n// ===== Archivo: src\\app\\app\\page.tsx (Corregido) =====\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n // <-- MODIFICACIÓN 1\n // <-- MODIFICACIÓN 2: Importación corregida\n\n\n\n\n\n\nfunction AppPage() {\n    var _user_email;\n    _s();\n    const [documentosSeleccionados, setDocumentosSeleccionados] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [mostrarUploader, setMostrarUploader] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('dashboard');\n    const [showUploadSuccess, setShowUploadSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRefreshingDocuments, setIsRefreshingDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [planEstudios, setPlanEstudios] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [temarioId, setTemarioId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [tienePlanificacion, setTienePlanificacion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showTokenStats, setShowTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [refreshSummaries, setRefreshSummaries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasStudyPlanningAccess, setHasStudyPlanningAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasAiTutorAccess, setHasAiTutorAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasSummaryAccess, setHasSummaryAccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [shouldRefreshTokenStats, setShouldRefreshTokenStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { cerrarSesion, user, isLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const documentSelectorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { plan: userPlan, isLoading: planLoading } = (0,_hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan)();\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { generatePlanEstudios, isGenerating } = (0,_hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration)();\n    const { latestResult, isLoading: isPlanLoading } = (0,_hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults)({\n        onResult: {\n            \"AppPage.usePlanEstudiosResults\": (result)=>{\n                setPlanEstudios(result);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('¡Plan de estudios generado exitosamente!');\n            }\n        }[\"AppPage.usePlanEstudiosResults\"],\n        onError: {\n            \"AppPage.usePlanEstudiosResults\": (error)=>{\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error(\"Error al generar plan: \".concat(error));\n            }\n        }[\"AppPage.usePlanEstudiosResults\"]\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            console.log('🔄 useEffect ejecutándose con:', {\n                isLoading,\n                planLoading,\n                user: user === null || user === void 0 ? void 0 : user.id,\n                userPlan,\n                timestamp: new Date().toISOString()\n            });\n            if (isLoading || planLoading || !user) {\n                console.log('⏳ Esperando datos:', {\n                    isLoading,\n                    planLoading,\n                    hasUser: !!user\n                });\n                return;\n            }\n            const checkInitialSetup = {\n                \"AppPage.useEffect.checkInitialSetup\": async ()=>{\n                    try {\n                        console.log('🔍 Verificando setup inicial:', {\n                            userPlan,\n                            userPlanType: typeof userPlan,\n                            userId: user === null || user === void 0 ? void 0 : user.id,\n                            timestamp: new Date().toISOString()\n                        });\n                        // VERIFICACIÓN DIRECTA DEL PLAN: Hacer una consulta directa para asegurar el plan correcto\n                        let planReal = userPlan;\n                        try {\n                            const response = await fetch('/api/user/plan');\n                            if (response.ok) {\n                                const data = await response.json();\n                                planReal = data.plan || 'free';\n                                console.log('📊 Plan verificado directamente:', {\n                                    planDelHook: userPlan,\n                                    planDeLaAPI: planReal,\n                                    sonIguales: userPlan === planReal\n                                });\n                            }\n                        } catch (apiError) {\n                            console.warn('⚠️ Error verificando plan directamente, usando hook:', apiError);\n                        }\n                        // Verificar si el usuario ya tiene un temario configurado\n                        const temarioConfigurado = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.tieneTemarioConfigurado)();\n                        console.log('📋 Temario configurado:', temarioConfigurado);\n                        // LÓGICA CLAVE: Solo mostrar setup para plan 'pro' sin temario\n                        console.log('🎯 Evaluando condición:', {\n                            planReal,\n                            esPro: planReal === 'pro',\n                            temarioConfigurado,\n                            condicionCompleta: planReal === 'pro' && !temarioConfigurado\n                        });\n                        if (planReal === 'pro' && !temarioConfigurado) {\n                            console.log('✅ Mostrando setup de temario para usuario pro sin temario');\n                            setMostrarSetupTemario(true);\n                        } else {\n                            console.log('🏠 Redirigiendo al dashboard:', {\n                                plan: planReal,\n                                tieneTemario: temarioConfigurado,\n                                razon: planReal !== 'pro' ? 'Plan no es pro' : 'Ya tiene temario configurado'\n                            });\n                            setMostrarSetupTemario(false);\n                            cargarDatosDashboard();\n                        }\n                    } catch (error) {\n                        console.error('❌ Error en checkInitialSetup:', error);\n                        // En caso de error, ir al dashboard por defecto (comportamiento seguro)\n                        console.log('🔄 Fallback: Redirigiendo al dashboard por error');\n                        setMostrarSetupTemario(false);\n                        cargarDatosDashboard();\n                    }\n                }\n            }[\"AppPage.useEffect.checkInitialSetup\"];\n            checkInitialSetup();\n            checkAccessFeatures();\n        }\n    }[\"AppPage.useEffect\"], [\n        user,\n        isLoading,\n        userPlan,\n        planLoading\n    ]);\n    const cargarDatosDashboard = async ()=>{\n        if (!user) return;\n        try {\n            const temario = await (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_25__.obtenerTemarioUsuario)();\n            if (temario) {\n                setTemarioId(temario.id);\n                const tienePlan = await (0,_features_planificacion_services_planificacionService__WEBPACK_IMPORTED_MODULE_26__.tienePlanificacionConfigurada)(temario.id);\n                setTienePlanificacion(tienePlan);\n                const planExistente = await (0,_features_planificacion_services_planEstudiosClientService__WEBPACK_IMPORTED_MODULE_27__.obtenerPlanEstudiosActivoCliente)(temario.id);\n                if (planExistente === null || planExistente === void 0 ? void 0 : planExistente.plan_data) {\n                    setPlanEstudios(planExistente.plan_data);\n                } else {\n                    setPlanEstudios(null);\n                }\n            } else {\n                setTemarioId(null);\n                setTienePlanificacion(false);\n                setPlanEstudios(null);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n        }\n    };\n    const checkAccessFeatures = async ()=>{\n        if (user) {\n            const [studyPlanningAccess, aiTutorAccess, summaryAccess] = await Promise.all([\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('study_planning'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('ai_tutor_chat'),\n                (0,_config_plans__WEBPACK_IMPORTED_MODULE_23__.checkUserFeatureAccess)('summary_a1_a2')\n            ]);\n            setHasStudyPlanningAccess(studyPlanningAccess);\n            setHasAiTutorAccess(aiTutorAccess);\n            setHasSummaryAccess(summaryAccess);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AppPage.useEffect\": ()=>{\n            if (latestResult) {\n                setPlanEstudios(latestResult);\n            }\n        }\n    }[\"AppPage.useEffect\"], [\n        latestResult\n    ]);\n    if (isLoading || planLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 198,\n            columnNumber: 7\n        }, this);\n    }\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        cargarDatosDashboard();\n    };\n    console.log('🎭 Estado de renderizado:', {\n        mostrarSetupTemario,\n        userPlan,\n        planLoading,\n        isLoading,\n        user: user === null || user === void 0 ? void 0 : user.id\n    });\n    if (mostrarSetupTemario) {\n        console.log('🎯 Renderizando TemarioSetup');\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioSetup__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n            lineNumber: 222,\n            columnNumber: 12\n        }, this);\n    }\n    const handleUploadSuccess = async ()=>{\n        setShowUploadSuccess(true);\n        setIsRefreshingDocuments(true);\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos:', error);\n        } finally{\n            setIsRefreshingDocuments(false);\n        }\n        setTimeout(()=>setShowUploadSuccess(false), 5000);\n    };\n    const handleDocumentDeleted = async ()=>{\n        try {\n            var _documentSelectorRef_current;\n            await ((_documentSelectorRef_current = documentSelectorRef.current) === null || _documentSelectorRef_current === void 0 ? void 0 : _documentSelectorRef_current.recargarDocumentos());\n        } catch (error) {\n            console.error('Error al recargar documentos después de eliminar:', error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await cerrarSesion();\n    };\n    const handleGenerarPlanEstudios = async ()=>{\n        if (!temarioId) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('No se encontró un temario configurado');\n            return;\n        }\n        if (!tienePlanificacion) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n            return;\n        }\n        try {\n            await generatePlanEstudios({\n                temarioId,\n                onComplete: (result)=>setPlanEstudios(result),\n                onError: (error)=>{\n                    if (error.includes('planificación configurada')) {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Necesitas completar la configuración de planificación en \"Mi Temario\"');\n                    } else {\n                        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.error('Error al generar el plan de estudios. Inténtalo de nuevo.');\n                    }\n                }\n            });\n        } catch (error) {\n            console.error('Error al iniciar generación del plan:', error);\n        }\n    };\n    const handleDescargarPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const blob = new Blob([\n            planTexto\n        ], {\n            type: 'text/markdown'\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = \"plan-estudios-\".concat(new Date().toISOString().split('T')[0], \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Plan descargado exitosamente');\n    };\n    const convertirPlanATexto = (plan)=>{\n        let texto = \"# Plan de Estudios Personalizado\\n\\n\";\n        texto += \"\".concat(plan.introduccion, \"\\n\\n\");\n        texto += \"## Resumen del Plan\\n\\n\";\n        texto += \"- **Tiempo total de estudio:** \".concat(plan.resumen.tiempoTotalEstudio, \"\\n\");\n        texto += \"- **N\\xfamero de temas:** \".concat(plan.resumen.numeroTemas, \"\\n\");\n        texto += \"- **Duraci\\xf3n estudio nuevo:** \".concat(plan.resumen.duracionEstudioNuevo, \"\\n\");\n        texto += \"- **Duraci\\xf3n repaso final:** \".concat(plan.resumen.duracionRepasoFinal, \"\\n\\n\");\n        texto += \"## Cronograma Semanal\\n\\n\";\n        plan.semanas.forEach((semana)=>{\n            texto += \"### Semana \".concat(semana.numero, \" (\").concat(semana.fechaInicio, \" - \").concat(semana.fechaFin, \")\\n\\n\");\n            texto += \"**Objetivo:** \".concat(semana.objetivoPrincipal, \"\\n\\n\");\n            semana.dias.forEach((dia)=>{\n                texto += \"**\".concat(dia.dia, \" (\").concat(dia.horas, \"h):**\\n\");\n                dia.tareas.forEach((tarea)=>{\n                    texto += \"- \".concat(tarea.titulo, \" (\").concat(tarea.duracionEstimada, \")\\n\");\n                    if (tarea.descripcion) {\n                        texto += \"  \".concat(tarea.descripcion, \"\\n\");\n                    }\n                });\n                texto += '\\n';\n            });\n        });\n        texto += \"## Estrategia de Repasos\\n\\n\".concat(plan.estrategiaRepasos, \"\\n\\n\");\n        texto += \"## Pr\\xf3ximos Pasos\\n\\n\".concat(plan.proximosPasos, \"\\n\");\n        return texto;\n    };\n    const handleImprimirPlan = ()=>{\n        if (!planEstudios) return;\n        const planTexto = convertirPlanATexto(planEstudios);\n        const printWindow = window.open('', '_blank');\n        if (printWindow) {\n            printWindow.document.write('\\n        <html>\\n          <head>\\n            <title>Plan de Estudios Personalizado</title>\\n            <style>\\n              body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }\\n              h1, h2, h3 { color: #333; }\\n              h1 { border-bottom: 2px solid #333; padding-bottom: 10px; }\\n              h2 { border-bottom: 1px solid #666; padding-bottom: 5px; }\\n              ul, ol { margin-left: 20px; }\\n              strong { color: #2563eb; }\\n              @media print { body { margin: 0; } }\\n            </style>\\n          </head>\\n          <body>\\n            <div id=\"content\"></div>\\n            <script>\\n              const markdown = '.concat(JSON.stringify(planTexto), \";\\n              const content = markdown\\n                .replace(/^# (.*$)/gim, '<h1>$1</h1>')\\n                .replace(/^## (.*$)/gim, '<h2>$1</h2>')\\n                .replace(/^### (.*$)/gim, '<h3>$1</h3>')\\n                .replace(/\\\\*\\\\*(.*?)\\\\*\\\\*/g, '<strong>$1</strong>')\\n                .replace(/\\\\*(.*?)\\\\*/g, '<em>$1</em>')\\n                .replace(/^- (.*$)/gim, '<li>$1</li>')\\n                .replace(/(<li>.*<\\\\/li>)/s, '<ul>$1</ul>')\\n                .replace(/\\\\n/g, '<br>');\\n              document.getElementById('content').innerHTML = content;\\n              window.print();\\n            </script>\\n          </body>\\n        </html>\\n      \"));\n            printWindow.document.close();\n        }\n    };\n    const handleNavigateToTab = (tab)=>{\n        setActiveTab(tab);\n    };\n    const handleSummaryGenerated = (summaryId)=>{\n        setRefreshSummaries((prev)=>prev + 1);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_29__.toast.success('Resumen generado exitosamente');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-12xl mx-auto px-4 sm:px-6 lg:px-8 py-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center -ml-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        src: \"/logo2.png\",\n                                        alt: \"OposiAI Logo\",\n                                        width: 80,\n                                        height: 80,\n                                        className: \"h-20 w-20 object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    \"Hola, \",\n                                                    (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>router.push('/profile'),\n                                                className: \"inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                                title: \"Ver perfil\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiUser, {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowTokenStats(true),\n                                        className: \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50\",\n                                        title: \"Ver estad\\xedsticas de uso de IA\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiBarChart, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Estad\\xedsticas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 406,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setMostrarUploader(!mostrarUploader),\n                                        className: \"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPlus, {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nuevo documento\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 414,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiLogOut, {\n                                                className: \"mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Cerrar sesi\\xf3n\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 391,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    mostrarUploader && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 transition-all duration-300 ease-in-out\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentUploader__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            onSuccess: handleUploadSuccess\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    showUploadSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6 p-4 bg-green-50 text-green-800 rounded-lg border border-green-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCheck, {\n                                    className: \"text-green-500 mr-2 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: \"\\xa1Documento subido exitosamente!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-700 mt-1\",\n                                            children: isRefreshingDocuments ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                        className: \"animate-spin mr-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \"Actualizando lista de documentos...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 21\n                                            }, this) : \"El documento ya está disponible en los desplegables de selección.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 446,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 444,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 441,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiFileText, {\n                                        className: \"w-4 h-4 text-blue-600 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-base font-semibold text-gray-900\",\n                                        children: \"Documentos Seleccionados\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-600 mb-2\",\n                                children: \"Selecciona los documentos que quieres usar para generar contenido con IA.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentSelector__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                ref: documentSelectorRef,\n                                onSelectionChange: setDocumentosSeleccionados\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this),\n                            documentosSeleccionados.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-blue-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-blue-800 font-medium\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: documentosSeleccionados.length\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" documento\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \" seleccionado\",\n                                            documentosSeleccionados.length !== 1 ? 's' : '',\n                                            \".\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-1 flex flex-wrap gap-1\",\n                                        children: documentosSeleccionados.map((doc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",\n                                                children: [\n                                                    doc.numero_tema && \"Tema \".concat(doc.numero_tema, \": \"),\n                                                    doc.titulo\n                                                ]\n                                            }, doc.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_SidebarMenu__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                activeTab: activeTab,\n                                onTabChange: setActiveTab\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: activeTab === 'dashboard' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_dashboard_components_Dashboard__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    onNavigateToTab: handleNavigateToTab\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-xl shadow-sm overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: [\n                                            activeTab === 'temario' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_temario_components_TemarioManager__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'planEstudios' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: !hasStudyPlanningAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                    feature: \"study_planning\",\n                                                    benefits: [\n                                                        \"Planes de estudio personalizados con IA\",\n                                                        \"Cronogramas adaptativos a tu ritmo\",\n                                                        \"Seguimiento automático de progreso\",\n                                                        \"Recomendaciones inteligentes de repaso\"\n                                                    ],\n                                                    className: \"min-h-[600px]\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 25\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-2xl font-semibold text-gray-900\",\n                                                                    children: \"Mi Plan de Estudios\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex gap-2\",\n                                                                    children: planEstudios && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleGenerarPlanEstudios,\n                                                                                disabled: isPlanLoading || isGenerating('plan-estudios'),\n                                                                                className: \"flex items-center gap-2 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiRefreshCw, {\n                                                                                        className: \"w-4 h-4 \".concat(isPlanLoading || isGenerating('plan-estudios') ? 'animate-spin' : '')\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 529,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Regenerar Plan\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 524,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleDescargarPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiDownload, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 536,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Descargar\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 532,\n                                                                                columnNumber: 35\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: handleImprimirPlan,\n                                                                                className: \"flex items-center gap-2 px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiPrinter, {\n                                                                                        className: \"w-4 h-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 543,\n                                                                                        columnNumber: 37\n                                                                                    }, this),\n                                                                                    \"Imprimir\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 539,\n                                                                                columnNumber: 35\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        isPlanLoading || isGenerating('plan-estudios') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                                    children: \"Generando tu plan personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600\",\n                                                                    children: \"La IA est\\xe1 analizando tu temario y configuraci\\xf3n...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 29\n                                                        }, this) : planEstudios && temarioId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_planificacion_components_PlanEstudiosViewer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            plan: planEstudios,\n                                                            temarioId: temarioId\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 29\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-12\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-20 h-20 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                        className: \"w-10 h-10 text-teal-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 564,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Genera tu Plan de Estudios Personalizado\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 566,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 mb-8 max-w-2xl mx-auto\",\n                                                                    children: \"Crea un plan de estudios personalizado basado en tu temario y configuraci\\xf3n de planificaci\\xf3n\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 569,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: handleGenerarPlanEstudios,\n                                                                    disabled: !tienePlanificacion,\n                                                                    className: \"inline-flex items-center px-6 py-3 bg-teal-600 text-white rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBarChart_FiCalendar_FiCheck_FiDownload_FiFileText_FiLogOut_FiPlus_FiPrinter_FiRefreshCw_FiUser_react_icons_fi__WEBPACK_IMPORTED_MODULE_33__.FiCalendar, {\n                                                                            className: \"w-5 h-5 mr-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        \"Generar Plan de Estudios\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 572,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                !tienePlanificacion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-4\",\n                                                                    children: 'Necesitas completar la configuraci\\xf3n de planificaci\\xf3n en \"Mi Temario\"'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 581,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                            lineNumber: 562,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 21\n                                            }, this),\n                                            activeTab === 'preguntas' && (!hasAiTutorAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"ai_tutor_chat\",\n                                                benefits: [\n                                                    \"Chat ilimitado con IA especializada\",\n                                                    \"Respuestas personalizadas a tus documentos\",\n                                                    \"Historial completo de conversaciones\",\n                                                    \"Explicaciones detalladas y ejemplos\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_conversations_components_QuestionForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'mapas' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_mindmaps_components_MindMapGenerator__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'flashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardGenerator__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 52\n                                            }, this),\n                                            activeTab === 'tests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestGenerator__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                documentosSeleccionados: documentosSeleccionados\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 47\n                                            }, this),\n                                            activeTab === 'misTests' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_tests_components_TestViewer__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 48\n                                            }, this),\n                                            activeTab === 'misFlashcards' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_flashcards_components_FlashcardViewer__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 53\n                                            }, this),\n                                            activeTab === 'resumenes' && (!hasSummaryAccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_UpgradePlanMessage__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                feature: \"summary_a1_a2\",\n                                                benefits: [\n                                                    \"Resúmenes inteligentes con IA\",\n                                                    \"Formato A1 y A2 optimizado\",\n                                                    \"Edición automática de contenido\",\n                                                    \"Exportación a PDF de alta calidad\"\n                                                ],\n                                                className: \"min-h-[600px]\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryGenerator__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        documentosSeleccionados: documentosSeleccionados,\n                                                        onSummaryGenerated: handleSummaryGenerated\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                                        className: \"border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_summaries_components_SummaryList__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        refreshTrigger: refreshSummaries\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                        lineNumber: 631,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 625,\n                                                columnNumber: 23\n                                            }, this)),\n                                            activeTab === 'gestionar' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_documents_components_DocumentManager__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onDocumentDeleted: handleDocumentDeleted\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 51\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 501,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t border-gray-200 mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gray-500 text-sm\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" OposiAI - Asistente para Oposiciones\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 647,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 md:mt-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex space-x-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"T\\xe9rminos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 653,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Privacidad\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 654,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#\",\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"Contacto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                            lineNumber: 655,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                    lineNumber: 644,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_MobileDebugInfo__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 662,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_shared_components_DiagnosticPanel__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 663,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TokenStatsModal__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                isOpen: showTokenStats,\n                onClose: ()=>{\n                    setShowTokenStats(false);\n                    setShouldRefreshTokenStats(false);\n                },\n                shouldRefreshOnOpen: shouldRefreshTokenStats\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n                lineNumber: 664,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\app\\\\app\\\\page.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s(AppPage, \"nDqoKpz+OWY9Y6K3b7WWUu4YOYk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_20__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useUserPlan__WEBPACK_IMPORTED_MODULE_32__.useUserPlan,\n        _hooks_useBackgroundGeneration__WEBPACK_IMPORTED_MODULE_21__.useBackgroundGeneration,\n        _hooks_usePlanEstudiosResults__WEBPACK_IMPORTED_MODULE_22__.usePlanEstudiosResults\n    ];\n});\n_c = AppPage;\nvar _c;\n$RefreshReg$(_c, \"AppPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/app/page.tsx\n"));

/***/ })

});