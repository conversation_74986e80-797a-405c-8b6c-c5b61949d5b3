import { NextRequest, NextResponse } from 'next/server';
import { SupabaseAdminService } from '@/lib/supabase/admin';
import { UserManagementService } from '@/lib/services/userManagement';

export async function POST(request: NextRequest) {
  try {
    const { sessionId } = await request.json();

    if (!sessionId) {
      return NextResponse.json({ 
        error: 'Session ID requerido' 
      }, { status: 400 });
    }

    // Verificar si existe la transacción
    const transaction = await SupabaseAdminService.getTransactionBySessionId(sessionId);
    
    if (!transaction) {
      return NextResponse.json({ 
        error: 'Transacción no encontrada' 
      }, { status: 404 });
    }

    // Si ya existe usuario, no hacer nada
    if (transaction.user_id) {
      const profile = await SupabaseAdminService.getUserProfile(transaction.user_id);
      if (profile && profile.payment_verified) {
        return NextResponse.json({
          success: true,
          message: 'Usuario ya existe y está verificado'
        });
      }
    }

    // Reintentar creación de usuario
    const result = await UserManagementService.createUserWithPlan({
      email: transaction.user_email,
      name: transaction.user_name || 'Usuario',
      planId: transaction.plan_id,
      stripeSessionId: transaction.stripe_session_id,
      stripeCustomerId: transaction.stripe_customer_id || '',
      amount: transaction.amount,
      currency: transaction.currency,
      subscriptionId: undefined
    });

    if (result.success) {

      return NextResponse.json({
        success: true,
        message: 'Cuenta reactivada exitosamente',
        userId: result.userId
      });
    } else {
      console.error('❌ Error reactivando cuenta:', result.error);
      return NextResponse.json({ 
        error: result.error || 'Error reactivando cuenta' 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('Error en reactivación:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}
