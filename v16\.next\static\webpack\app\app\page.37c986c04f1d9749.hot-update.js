"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/app/page",{

/***/ "(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx":
/*!*********************************************************!*\
  !*** ./src/features/dashboard/components/Dashboard.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiBook,FiBookOpen,FiCalendar,FiCheckSquare,FiClock,FiFileText,FiLayers,FiPlay,FiPlus,FiTarget,FiTrendingUp!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/supabase/dashboardService */ \"(app-pages-browser)/./src/lib/supabase/dashboardService.ts\");\n/* harmony import */ var _features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/features/temario/services/temarioService */ \"(app-pages-browser)/./src/features/temario/services/temarioService.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nconst Dashboard = (param)=>{\n    let { onNavigateToTab } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [estadisticas, setEstadisticas] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [proximasFlashcards, setProximasFlashcards] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [mostrarSetupTemario, setMostrarSetupTemario] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            cargarDatos();\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const cargarDatos = async ()=>{\n        setIsLoading(true);\n        try {\n            const [statsData, proximasData, temarioConfigurado] = await Promise.all([\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerEstadisticasDashboard)(),\n                (0,_lib_supabase_dashboardService__WEBPACK_IMPORTED_MODULE_3__.obtenerProximasFlashcards)(5),\n                (0,_features_temario_services_temarioService__WEBPACK_IMPORTED_MODULE_4__.tieneTemarioConfigurado)()\n            ]);\n            setEstadisticas(statsData);\n            setProximasFlashcards(proximasData);\n            // Si no tiene temario configurado, mostrar setup automáticamente\n            if (!temarioConfigurado) {\n                setMostrarSetupTemario(true);\n            }\n        } catch (error) {\n            console.error('Error al cargar datos del dashboard:', error);\n            // En caso de error, usar datos por defecto\n            setEstadisticas({\n                totalDocumentos: 0,\n                totalColeccionesFlashcards: 0,\n                totalTests: 0,\n                totalFlashcards: 0,\n                flashcardsParaHoy: 0,\n                flashcardsNuevas: 0,\n                flashcardsAprendiendo: 0,\n                flashcardsRepasando: 0,\n                testsRealizados: 0,\n                porcentajeAcierto: 0,\n                coleccionesRecientes: [],\n                testsRecientes: []\n            });\n            setProximasFlashcards([]);\n            setMostrarSetupTemario(true);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleTemarioSetupComplete = ()=>{\n        setMostrarSetupTemario(false);\n        // Recargar datos del dashboard\n        cargarDatos();\n    };\n    const obtenerSaludo = ()=>{\n        const hora = new Date().getHours();\n        if (hora < 12) return 'Buenos días';\n        if (hora < 18) return 'Buenas tardes';\n        return 'Buenas noches';\n    };\n    const obtenerNombreUsuario = ()=>{\n        var _user_email;\n        return (user === null || user === void 0 ? void 0 : (_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.split('@')[0]) || 'Estudiante';\n    };\n    // Mostrar setup de temario si es necesario\n    if (mostrarSetupTemario) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TemarioSetup, {\n            onComplete: handleTemarioSetupComplete\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 94,\n            columnNumber: 12\n        }, undefined);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold mb-2\",\n                        children: [\n                            obtenerSaludo(),\n                            \", \",\n                            obtenerNombreUsuario(),\n                            \"! \\uD83D\\uDC4B\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-blue-100\",\n                        children: \"\\xbfListo para continuar con tu preparaci\\xf3n? Aqu\\xed tienes un resumen de tu progreso.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Documentos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalDocumentos) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiFileText, {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Colecciones\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalColeccionesFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiBook, {\n                                    className: \"h-8 w-8 text-emerald-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Tests\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalTests) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCheckSquare, {\n                                    className: \"h-8 w-8 text-pink-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-4 shadow-sm border\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600\",\n                                            children: \"Flashcards\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.totalFlashcards) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTarget, {\n                                    className: \"h-8 w-8 text-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900\",\n                                children: \"Estudio de Hoy\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCalendar, {\n                                className: \"h-6 w-6 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 rounded-lg p-4 border border-orange-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-orange-800\",\n                                                    children: \"Para Repasar Hoy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsParaHoy) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiClock, {\n                                            className: \"h-6 w-6 text-orange-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 rounded-lg p-4 border border-blue-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Nuevas\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.flashcardsNuevas) || 0\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiBookOpen, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 rounded-lg p-4 border border-green-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"% Acierto Tests\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        (estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.porcentajeAcierto.toFixed(1)) || 0,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiTrendingUp, {\n                                            className: \"h-6 w-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, undefined),\n                    estadisticas && estadisticas.flashcardsParaHoy > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>onNavigateToTab('misFlashcards'),\n                            className: \"bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg flex items-center transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPlay, {\n                                    className: \"mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Comenzar Estudio\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            proximasFlashcards.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Pr\\xf3ximas Flashcards\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: proximasFlashcards.slice(0, 3).map((flashcard)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 truncate\",\n                                                children: flashcard.pregunta\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: flashcard.coleccionTitulo\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(flashcard.estado === 'nuevo' ? 'bg-blue-100 text-blue-800' : flashcard.estado === 'aprendiendo' ? 'bg-yellow-100 text-yellow-800' : flashcard.estado === 'repasando' ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'),\n                                            children: flashcard.estado\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, flashcard.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, undefined),\n                    proximasFlashcards.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onNavigateToTab('misFlashcards'),\n                        className: \"mt-3 text-blue-600 hover:text-blue-800 text-sm font-medium\",\n                        children: \"Ver todas las flashcards pendientes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 214,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Colecciones Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.coleccionesRecientes.map((coleccion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: coleccion.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creada: \",\n                                                            new Date(coleccion.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                    children: [\n                                                        coleccion.paraHoy,\n                                                        \" para hoy\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, coleccion.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misFlashcards'),\n                                className: \"mt-3 text-emerald-600 hover:text-emerald-800 text-sm font-medium\",\n                                children: \"Ver todas las colecciones\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 mb-4\",\n                                children: \"Tests Recientes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: estadisticas === null || estadisticas === void 0 ? void 0 : estadisticas.testsRecientes.map((test)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 bg-gray-50 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900\",\n                                                        children: test.titulo\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            \"Creado: \",\n                                                            new Date(test.fechaCreacion).toLocaleDateString('es-ES')\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-pink-100 text-pink-800\",\n                                                    children: [\n                                                        test.numeroPreguntas,\n                                                        \" preguntas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, test.id, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('misTests'),\n                                className: \"mt-3 text-pink-600 hover:text-pink-800 text-sm font-medium\",\n                                children: \"Ver todos los tests\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl p-6 shadow-sm border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                        children: \"Acciones R\\xe1pidas\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('preguntas'),\n                                className: \"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiFileText, {\n                                        className: \"h-6 w-6 text-blue-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-blue-900\",\n                                        children: \"Hacer Preguntas\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('flashcards'),\n                                className: \"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors border border-orange-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiPlus, {\n                                        className: \"h-6 w-6 text-orange-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-orange-900\",\n                                        children: \"Crear Flashcards\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('tests'),\n                                className: \"flex items-center p-4 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors border border-indigo-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCheckSquare, {\n                                        className: \"h-6 w-6 text-indigo-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-indigo-900\",\n                                        children: \"Generar Tests\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('mapas'),\n                                className: \"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors border border-purple-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLayers, {\n                                        className: \"h-6 w-6 text-purple-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-purple-900\",\n                                        children: \"Mapas Mentales\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>onNavigateToTab('planEstudios'),\n                                className: \"flex items-center p-4 bg-teal-50 hover:bg-teal-100 rounded-lg transition-colors border border-teal-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBook_FiBookOpen_FiCalendar_FiCheckSquare_FiClock_FiFileText_FiLayers_FiPlay_FiPlus_FiTarget_FiTrendingUp_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiCalendar, {\n                                        className: \"h-6 w-6 text-teal-600 mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-teal-900\",\n                                        children: \"Plan de Estudios\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n                lineNumber: 307,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\OposI\\\\v16\\\\src\\\\features\\\\dashboard\\\\components\\\\Dashboard.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Dashboard, \"WXVoIEO+51ySqZNmMuO5W11HHFA=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = Dashboard;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/features/dashboard/components/Dashboard.tsx\n"));

/***/ })

});