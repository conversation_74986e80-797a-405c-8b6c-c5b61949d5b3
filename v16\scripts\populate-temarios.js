const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Configuración de Supabase
const supabaseUrl = 'https://fxnhpxjijinfuxxxplzj.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseKey) {
  console.error('❌ Error: SUPABASE_SERVICE_ROLE_KEY no está configurada');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Mapeo de archivos a información de temarios
const temarioMapping = {
  'a1_2019_junta.md': {
    titulo: 'Cuerpo Superior Facultativo - Informática (A1.2019)',
    descripcion: 'Temario para oposiciones del Cuerpo Superior Facultativo, opción Informática, Junta de Andalucía 2019',
    tipo: 'informatica'
  },
  'c1_junta.md': {
    titulo: 'Cuerpo Técnico - Grupo C1',
    descripcion: 'Temario para oposiciones del Cuerpo Técnico, Grupo C1, Junta de Andalucía',
    tipo: 'tecnico'
  },
  'c2_estado.md': {
    titulo: 'Cuerpo Auxiliar - Grupo C2 (Estado)',
    descripcion: 'Temario para oposiciones del Cuerpo Auxiliar, Grupo C2, Administración del Estado',
    tipo: 'auxiliar'
  },
  'c2_junta.md': {
    titulo: 'Cuerpo Auxiliar - Grupo C2 (Junta)',
    descripcion: 'Temario para oposiciones del Cuerpo Auxiliar, Grupo C2, Junta de Andalucía',
    tipo: 'auxiliar'
  }
};

// Función para extraer temas de un archivo markdown
function extraerTemas(contenido) {
  const temas = [];
  const lineas = contenido.split('\n');
  
  let numeroTema = 1;
  
  for (const linea of lineas) {
    // Buscar líneas que empiecen con "Tema X." donde X es un número
    const match = linea.match(/^Tema\s+(\d+)\.\s*(.+)$/);
    if (match) {
      const numero = parseInt(match[1]);
      const titulo = match[2].trim();
      
      temas.push({
        numero: numero,
        titulo: titulo,
        descripcion: titulo, // Usar el título como descripción por ahora
        orden: numero
      });
    }
  }
  
  return temas;
}

// Función principal para poblar temarios
async function poblarTemarios() {
  console.log('🚀 Iniciando población de temarios...');
  
  const temarioDir = path.join(__dirname, '..', 'public', 'temarios');
  
  try {
    // Leer archivos del directorio
    const archivos = fs.readdirSync(temarioDir).filter(archivo => archivo.endsWith('.md'));
    
    console.log(`📁 Encontrados ${archivos.length} archivos de temarios`);
    
    for (const archivo of archivos) {
      if (!temarioMapping[archivo]) {
        console.log(`⚠️  Saltando archivo no mapeado: ${archivo}`);
        continue;
      }
      
      console.log(`\n📖 Procesando: ${archivo}`);
      
      const rutaArchivo = path.join(temarioDir, archivo);
      const contenido = fs.readFileSync(rutaArchivo, 'utf8');
      
      // Extraer información del temario
      const infoTemario = temarioMapping[archivo];
      
      // Crear temario en Supabase
      const { data: temario, error: errorTemario } = await supabase
        .from('temarios')
        .insert({
          titulo: infoTemario.titulo,
          descripcion: infoTemario.descripcion,
          tipo: infoTemario.tipo
        })
        .select()
        .single();
      
      if (errorTemario) {
        console.error(`❌ Error creando temario ${archivo}:`, errorTemario);
        continue;
      }
      
      console.log(`✅ Temario creado: ${temario.titulo} (ID: ${temario.id})`);
      
      // Extraer temas del contenido
      const temas = extraerTemas(contenido);
      console.log(`📝 Extraídos ${temas.length} temas`);
      
      // Insertar temas en Supabase
      if (temas.length > 0) {
        const temasConTemarioId = temas.map(tema => ({
          ...tema,
          temario_id: temario.id,
          completado: false
        }));
        
        const { data: temasInsertados, error: errorTemas } = await supabase
          .from('temas')
          .insert(temasConTemarioId)
          .select();
        
        if (errorTemas) {
          console.error(`❌ Error insertando temas para ${archivo}:`, errorTemas);
        } else {
          console.log(`✅ Insertados ${temasInsertados.length} temas`);
        }
      }
    }
    
    // Mostrar resumen final
    const { data: totalTemarios } = await supabase
      .from('temarios')
      .select('id', { count: 'exact' });
    
    const { data: totalTemas } = await supabase
      .from('temas')
      .select('id', { count: 'exact' });
    
    console.log('\n🎉 ¡Población completada!');
    console.log(`📊 Resumen:`);
    console.log(`   - Temarios creados: ${totalTemarios?.length || 0}`);
    console.log(`   - Temas creados: ${totalTemas?.length || 0}`);
    
  } catch (error) {
    console.error('❌ Error general:', error);
  }
}

// Ejecutar script
poblarTemarios();
