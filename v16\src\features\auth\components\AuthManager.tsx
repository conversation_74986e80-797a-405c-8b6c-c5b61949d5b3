'use client';

import { useEffect } from 'react';
import { supabase } from '@/lib/supabase/supabaseClient';

/**
 * Componente para manejar errores comunes de autenticación
 * y sincronización de tiempo en Supabase
 */
export default function AuthManager() {
  useEffect(() => {
    // Verificar si hay problemas de sincronización de tiempo
    const checkTimeSync = async () => {
      try {
        // Usar variables de entorno para la configuración de Supabase
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
        const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

        if (!supabaseUrl || !supabaseKey) {
          console.warn('Variables de entorno de Supabase no configuradas');
          return;
        }

        // Obtener la hora del servidor de Supabase
        const response = await fetch(`${supabaseUrl}/rest/v1/`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'apikey': supabaseKey
          }
        });

        // Obtener la fecha del servidor desde las cabeceras
        const serverDate = new Date(response.headers.get('date') || '');
        const clientDate = new Date();

        // Calcular la diferencia en segundos
        const timeDiff = Math.abs((serverDate.getTime() - clientDate.getTime()) / 1000);

        // Si la diferencia es mayor a 60 segundos, mostrar una advertencia
        if (timeDiff > 60) {
          console.warn(
            `Posible problema de sincronización de tiempo detectado. ` +
            `La diferencia entre tu hora local y el servidor es de ${Math.round(timeDiff)} segundos. ` +
            `Esto puede causar problemas de autenticación.`
          );
        }
      } catch (error) {
        console.error('Error al verificar sincronización de tiempo:', error);
      }
    };

    // Ejecutar la verificación
    checkTimeSync();

    // Configurar un listener para eventos de autenticación
    const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {

      if (event === 'SIGNED_OUT') {
        // Supabase ya maneja la limpieza de tokens internamente
        
      } else if (event === 'SIGNED_IN') {
        
      }
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, []);

  // Este componente no renderiza nada visible
  return null;
}
