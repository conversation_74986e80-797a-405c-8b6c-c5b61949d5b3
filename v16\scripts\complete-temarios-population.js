// Script para completar la población de temarios desde archivos markdown
// Este script procesa todos los archivos .md y los inserta en Supabase

const fs = require('fs');
const path = require('path');

// Configuración de temarios
const temarioConfigs = [
  {
    archivo: 'c1_junta.md',
    titulo: 'Cuerpo Técnico - Grupo C1 (Junta)',
    descripcion: 'Temario para oposiciones del Cuerpo Técnico, Grupo C1, Junta de Andalucía',
    tipo: 'completo'
  },
  {
    archivo: 'c2_estado.md',
    titulo: 'Cuerpo Auxiliar - Grupo C2 (Estado)',
    descripcion: 'Temario para oposiciones del Cuerpo Auxiliar, Grupo C2, Administración del Estado',
    tipo: 'completo'
  },
  {
    archivo: 'c2_junta.md',
    titulo: 'Cuerpo Auxiliar - Grupo C2 (Junta)',
    descripcion: 'Temario para oposiciones del Cuerpo Auxiliar, Grupo C2, Junta de Andalucía',
    tipo: 'completo'
  }
];

// Función para extraer temas de contenido markdown
function extraerTemas(contenido) {
  const temas = [];
  const lineas = contenido.split('\n');
  
  for (const linea of lineas) {
    // Buscar líneas que empiecen con "Tema X." donde X es un número
    const match = linea.match(/^Tema\s+(\d+)\.\s*(.+)$/);
    if (match) {
      const numero = parseInt(match[1]);
      const titulo = match[2].trim();
      
      // Extraer título corto (hasta el primer punto o dos puntos)
      const tituloCorto = titulo.split(/[:.]/)[0].trim();
      
      temas.push({
        numero: numero,
        titulo: tituloCorto,
        descripcion: titulo,
        orden: numero
      });
    }
  }
  
  return temas;
}

// Función para generar SQL de inserción de temario
function generarSQLTemario(config) {
  return `INSERT INTO temarios (titulo, descripcion, tipo) VALUES ('${config.titulo.replace(/'/g, "''")}', '${config.descripcion.replace(/'/g, "''")}', '${config.tipo}') RETURNING id, titulo;`;
}

// Función para generar SQL de inserción de temas
function generarSQLTemas(temarioId, temas) {
  if (temas.length === 0) return '';
  
  const valores = temas.map(tema => 
    `('${temarioId}', ${tema.numero}, '${tema.titulo.replace(/'/g, "''")}', '${tema.descripcion.replace(/'/g, "''")}', ${tema.orden}, false)`
  ).join(', ');
  
  return `INSERT INTO temas (temario_id, numero, titulo, descripcion, orden, completado) VALUES ${valores};`;
}

// Función principal
function procesarTemarios() {
  console.log('🚀 Generando SQLs para población de temarios...\n');
  
  const temarioDir = path.join(__dirname, '..', 'public', 'temarios');
  let sqlCompleto = '-- Script de población de temarios\n-- Generado automáticamente\n\n';
  
  // Procesar cada configuración de temario
  for (const config of temarioConfigs) {
    const rutaArchivo = path.join(temarioDir, config.archivo);
    
    if (!fs.existsSync(rutaArchivo)) {
      console.log(`⚠️  Archivo no encontrado: ${config.archivo}`);
      continue;
    }
    
    console.log(`📖 Procesando: ${config.archivo}`);
    
    const contenido = fs.readFileSync(rutaArchivo, 'utf8');
    const temas = extraerTemas(contenido);
    
    console.log(`   - Extraídos ${temas.length} temas`);
    
    // Generar SQL para temario
    sqlCompleto += `-- Temario: ${config.titulo}\n`;
    sqlCompleto += generarSQLTemario(config) + '\n\n';
    
    // Generar SQL para temas (usando placeholder para ID)
    if (temas.length > 0) {
      sqlCompleto += `-- Temas para: ${config.titulo}\n`;
      sqlCompleto += `-- NOTA: Reemplazar 'TEMARIO_ID_${config.archivo.replace('.md', '').toUpperCase()}' con el ID real del temario\n`;
      sqlCompleto += generarSQLTemas(`TEMARIO_ID_${config.archivo.replace('.md', '').toUpperCase()}`, temas) + '\n\n';
    }
  }
  
  // Guardar SQL generado
  const sqlPath = path.join(__dirname, 'temarios-population.sql');
  fs.writeFileSync(sqlPath, sqlCompleto, 'utf8');
  
  console.log(`\n✅ SQL generado en: ${sqlPath}`);
  console.log('📋 Para ejecutar:');
  console.log('   1. Revisar el archivo SQL generado');
  console.log('   2. Reemplazar los placeholders TEMARIO_ID_* con los IDs reales');
  console.log('   3. Ejecutar en Supabase');
}

// Ejecutar
procesarTemarios();
