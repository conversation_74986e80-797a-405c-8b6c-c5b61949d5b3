// src/app/api/stripe/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe/config';
import { headers } from 'next/headers';
import { StripeWebhookHandlers } from '@/lib/services/stripeWebhookHandlers';

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const body = await request.text();
    const headersList = await headers();
    const signature = headersList.get('stripe-signature');

    if (!signature) {
      console.error('❌ No Stripe signature found');
      return NextResponse.json(
        { error: 'No signature found' },
        { status: 400 }
      );
    }

    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      console.error('❌ No webhook secret configured');
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      );
    }

    if (!stripe) {
      console.error('❌ Stripe not initialized');
      return NextResponse.json(
        { error: 'Stripe not configured' },
        { status: 500 }
      );
    }

    // Verificar el evento de webhook
    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('❌ Webhook signature verification failed:', err);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

     ('🎯 Webhook event received:', event.type, 'ID:', event.id);

    // Manejar diferentes tipos de eventos con manejadores específicos
    let result;

    switch (event.type) {
      case 'checkout.session.completed':
        result = await StripeWebhookHandlers.handleCheckoutSessionCompleted(event.data.object);
        break;

      case 'payment_intent.succeeded':
        result = await StripeWebhookHandlers.handlePaymentIntentSucceeded(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        result = await StripeWebhookHandlers.handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'customer.subscription.created':
        result = await StripeWebhookHandlers.handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        result = await StripeWebhookHandlers.handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        result = await StripeWebhookHandlers.handleSubscriptionDeleted(event.data.object);
        break;

      default:
         ('⚠️ Unhandled event type:', event.type);
        result = {
          success: true,
          message: `Event type ${event.type} not handled`
        };
    }

    // Log del resultado
    const processingTime = Date.now() - startTime;
     (`✅ Webhook processed in ${processingTime}ms:`, {
      eventType: event.type,
      eventId: event.id,
      success: result.success,
      message: result.message
    });

    // Responder según el resultado
    if (result.success) {
      return NextResponse.json({
        received: true,
        processed: true,
        eventType: event.type,
        message: result.message,
        data: result.data,
        processingTime
      });
    } else {
      console.error('❌ Webhook processing failed:', result.error);
      return NextResponse.json({
        received: true,
        processed: false,
        eventType: event.type,
        error: result.message,
        details: result.error
      }, { status: 422 }); // Unprocessable Entity
    }

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ Critical webhook error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      processingTime
    });

    return NextResponse.json(
      {
        error: 'Webhook handler failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        processingTime
      },
      { status: 500 }
    );
  }
}
