// src/features/profile/components/AccountSettings.tsx
// Componente para configuración y preferencias de la cuenta

'use client';

import React, { useState, useEffect } from 'react';
import {
  FiSettings,
  FiBell,
  FiShield,
  FiSave,
  FiCheck,
  FiLogOut,
  FiCreditCard,
  FiMail,
  FiLoader,
  FiX
} from 'react-icons/fi';
import { useRouter } from 'next/navigation';
import { createClient } from '@/lib/supabase/supabaseClient';
import { toast } from 'react-hot-toast';

interface UserProfile {
  user: {
    id: string;
    email: string;
    name: string;
    created_at: string;
  };
  profile: {
    subscription_plan: string;
    plan_expires_at?: string;
    auto_renew: boolean;
    payment_verified: boolean;
    last_payment_date?: string;
    security_flags?: {
      email_notifications?: boolean;
      marketing_emails?: boolean;
      [key: string]: any;
    };
  };
  access: {
    plan: string;
    features: string[];
    limits: any;
    currentUsage: any;
    paymentVerified: boolean;
  };
}

interface AccountSettingsProps {
  userProfile: UserProfile;
  onUpdate: () => void;
}

export default function AccountSettings({ userProfile, onUpdate }: AccountSettingsProps) {
  const router = useRouter();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  // Estados de preferencias
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);

  // Estados para cambio de contraseña
  const [showResetModal, setShowResetModal] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  // Cargar preferencias guardadas cuando el componente se monta o userProfile cambia
  useEffect(() => {
    if (userProfile?.profile?.security_flags) {
      const flags = userProfile.profile.security_flags;
      setEmailNotifications(flags.email_notifications ?? true);
      setMarketingEmails(flags.marketing_emails ?? false);
    }
  }, [userProfile]);

  // Función para solicitar reseteo de contraseña
  const handleRequestPasswordReset = async () => {
    setIsResetting(true);
    let loadingToastId: string | undefined;

    try {
      loadingToastId = toast.loading('Enviando enlace de recuperación...');

      const supabase = createClient();
      const { error: resetError } = await supabase.auth.resetPasswordForEmail(
        userProfile.user.email,
        {
          redirectTo: `${window.location.origin}/auth/reset-password`,
        }
      );

      if (resetError) {
        throw resetError;
      }

      toast.success('Se ha enviado un enlace a tu email para restablecer la contraseña.', {
        id: loadingToastId,
        duration: 6000
      });
      setShowResetModal(false);

    } catch (error) {
      console.error('Error al solicitar reseteo de contraseña:', error);
      toast.error('No se pudo enviar el enlace de recuperación. Inténtalo de nuevo más tarde.', {
        id: loadingToastId
      });
    } finally {
      setIsResetting(false);
    }
  };

  const handleSavePreferences = async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferences: {
            email_notifications: emailNotifications,
            marketing_emails: marketingEmails,
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Error actualizando preferencias');
      }

      setSuccess(true);
      onUpdate();

      // Limpiar mensaje de éxito después de 3 segundos
      setTimeout(() => setSuccess(false), 3000);

    } catch (error) {
      console.error('Error updating preferences:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
    } finally {
      setSaving(false);
    }
  };

  const handleLogout = async () => {
    try {
      const supabase = createClient();
      await supabase.auth.signOut();
      router.push('/');
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setSaving(true);
      setError(null);

      const response = await fetch('/api/user/cancel-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Error cancelando suscripción');
      }

      setSuccess(true);
      setShowCancelConfirm(false);
      onUpdate(); // Refrescar datos del usuario

      // Mostrar mensaje de éxito con información del período
      toast.success(
        `Suscripción cancelada exitosamente. Mantendrás acceso hasta: ${new Date(data.details.periodEnd).toLocaleDateString('es-ES')}`,
        { duration: 6000 }
      );

      setTimeout(() => setSuccess(false), 5000);

    } catch (error) {
      console.error('Error canceling subscription:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
      setShowCancelConfirm(false);
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Configuración de la Cuenta</h2>
      </div>

      {/* Mensajes de estado */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <span className="text-red-700">{error}</span>
        </div>
      )}

      {success && (
        <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md flex items-center">
          <FiCheck className="w-5 h-5 text-green-500 mr-2" />
          <span className="text-green-700">Configuración actualizada correctamente</span>
        </div>
      )}

      <div className="space-y-6">
        {/* Preferencias de Notificaciones */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <FiBell className="w-5 h-5 text-blue-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Preferencias de Notificaciones</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">
                  Notificaciones por Email
                </label>
                <p className="text-xs text-gray-600">
                  Recibir notificaciones importantes sobre tu cuenta y suscripción
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={emailNotifications}
                  onChange={(e) => setEmailNotifications(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">
                  Emails de Marketing
                </label>
                <p className="text-xs text-gray-600">
                  Recibir información sobre nuevas funciones y ofertas especiales
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={marketingEmails}
                  onChange={(e) => setMarketingEmails(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

          </div>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={handleSavePreferences}
              disabled={saving}
              className="flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              {saving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <FiSave className="w-4 h-4 mr-2" />
              )}
              {saving ? 'Guardando...' : 'Guardar Preferencias'}
            </button>
          </div>
        </div>

        {/* Configuración de Seguridad */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <FiShield className="w-5 h-5 text-green-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Seguridad</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">
                  Cambiar Contraseña
                </label>
                <p className="text-xs text-gray-600">
                  Recibirás un enlace en tu email para establecer una nueva contraseña.
                </p>
              </div>
              <button
                onClick={() => setShowResetModal(true)}
                className="bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm"
              >
                Cambiar
              </button>
            </div>

          </div>
        </div>

        {/* Acciones de Cuenta */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center mb-4">
            <FiSettings className="w-5 h-5 text-gray-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900">Acciones de Cuenta</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="text-sm font-medium text-gray-900">
                  Cerrar Sesión
                </label>
                <p className="text-xs text-gray-600">
                  Cerrar sesión en este dispositivo
                </p>
              </div>
              <button
                onClick={handleLogout}
                className="flex items-center bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors text-sm"
              >
                <FiLogOut className="w-4 h-4 mr-2" />
                Cerrar Sesión
              </button>
            </div>

            {/* Cancelar Suscripción - Solo para usuarios con plan de pago */}
            {userProfile.profile.subscription_plan !== 'free' && (
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-900">
                    Cancelar Suscripción
                  </label>
                  <p className="text-xs text-gray-600">
                    Cancelar tu suscripción actual (mantendrás acceso hasta el final del período)
                  </p>
                </div>
                <button
                  onClick={() => setShowCancelConfirm(true)}
                  className="flex items-center bg-red-100 text-red-700 px-4 py-2 rounded-md hover:bg-red-200 transition-colors text-sm"
                >
                  <FiCreditCard className="w-4 h-4 mr-2" />
                  Cancelar
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal de reseteo de contraseña */}
      {showResetModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <FiMail className="w-6 h-6 text-blue-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Restablecer Contraseña</h3>
            </div>

            <p className="text-gray-600 mb-6">
              Se enviará un enlace seguro a tu dirección de correo electrónico <strong>({userProfile.user.email})</strong> para que puedas establecer una nueva contraseña.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowResetModal(false)}
                disabled={isResetting}
                className="px-4 py-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleRequestPasswordReset}
                disabled={isResetting}
                className="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-lg transition-colors disabled:opacity-50 flex items-center"
              >
                {isResetting ? <FiLoader className="animate-spin mr-2" /> : <FiMail className="mr-2" />}
                {isResetting ? 'Enviando...' : 'Enviar Enlace'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de confirmación de cancelación de suscripción */}
      {showCancelConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center mb-4">
              <FiCreditCard className="w-6 h-6 text-red-500 mr-2" />
              <h3 className="text-lg font-semibold text-gray-900">Confirmar Cancelación</h3>
            </div>

            <p className="text-gray-600 mb-6">
              ¿Estás seguro de que quieres cancelar tu suscripción? Mantendrás acceso a las funciones premium hasta el final de tu período de facturación actual.
            </p>

            <div className="flex gap-3">
              <button
                onClick={handleCancelSubscription}
                disabled={saving}
                className="flex-1 bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center justify-center"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Cancelando...
                  </>
                ) : (
                  'Sí, cancelar suscripción'
                )}
              </button>
              <button
                onClick={() => setShowCancelConfirm(false)}
                className="flex-1 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors"
              >
                Mantener suscripción
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
