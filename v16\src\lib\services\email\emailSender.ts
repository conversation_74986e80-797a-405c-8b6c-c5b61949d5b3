// src/lib/services/email/emailSender.ts
// Lógica de envío de emails con reintentos y manejo de errores

import { EmailNotification, RetryResult } from './types';
import { EmailLogger } from './emailLogger';

export class EmailSender {
  
  /**
   * Función base para enviar emails con reintentos y manejo de errores
   * NOTA: Implementar con tu proveedor de email preferido (SendGrid, Resend, etc.)
   */
  static async sendEmail(notification: EmailNotification, retryCount: number = 0): Promise<boolean> {
    const maxRetries = 3;
    const retryDelay = Math.pow(2, retryCount) * 1000; // Backoff exponencial: 1s, 2s, 4s
    
    let notificationId: string | null = null;
    
    try {
       (`📧 Enviando email (intento ${retryCount + 1}/${maxRetries + 1}):`, {
        to: notification.to,
        subject: notification.subject,
        type: notification.type
      });

      // Registrar notificación como 'pending' antes del envío
      notificationId = await EmailLogger.logEmailNotification(notification, 'pending');

      // TODO: Implementar con tu proveedor de email real
      // Ejemplo con Resend:
      /*
      const resend = new Resend(process.env.RESEND_API_KEY);
      const result = await resend.emails.send({
        from: 'OposI <<EMAIL>>',
        to: notification.to,
        subject: notification.subject,
        html: notification.htmlContent,
        text: notification.textContent,
      });
      
      if (result.error) {
        throw new Error(`Resend API error: ${result.error.message}`);
      }
      */

      // Simulación de envío (remover cuando implementes proveedor real)
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Simular fallo ocasional para testing (remover en producción)
      if (Math.random() < 0.1 && retryCount === 0) {
        throw new Error('Simulated email provider error');
      }

      // Actualizar estado a 'sent' si el envío fue exitoso
      if (notificationId) {
        await EmailLogger.updateEmailNotificationStatus(notificationId, 'sent');
      }

       ('✅ Email enviado exitosamente');
      return true;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error enviando email (intento ${retryCount + 1}):`, errorMessage);

      // Actualizar estado a 'failed' si tenemos el ID
      if (notificationId) {
        await EmailLogger.updateEmailNotificationStatus(
          notificationId, 
          'failed', 
          errorMessage
        );
      }

      // Intentar reenvío si no hemos alcanzado el máximo de reintentos
      if (retryCount < maxRetries) {
         (`🔄 Reintentando envío en ${retryDelay}ms...`);
        
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return this.sendEmail(notification, retryCount + 1);
      }

      // Si agotamos los reintentos, registrar fallo final
      console.error(`💥 Fallo definitivo después de ${maxRetries + 1} intentos`);
      return false;
    }
  }

  /**
   * Reenviar notificaciones fallidas
   */
  static async retryFailedNotifications(
    maxAge: number = 24, // Máximo 24 horas de antigüedad
    limit: number = 10   // Máximo 10 reintentos por ejecución
  ): Promise<RetryResult> {
    try {
       (`🔄 Buscando notificaciones fallidas para reintentar (máximo ${maxAge} horas)...`);
      
      // Obtener notificaciones fallidas
      const failedNotifications = await EmailLogger.getFailedNotifications(maxAge, limit);

      if (failedNotifications.length === 0) {
         ('✅ No se encontraron notificaciones fallidas para reintentar');
        return { attempted: 0, successful: 0, failed: 0, errors: [] };
      }

       (`📋 Encontradas ${failedNotifications.length} notificaciones para reintentar`);

      let successful = 0;
      let failed = 0;
      const errors: string[] = [];

      // Reintentar cada notificación
      for (const notification of failedNotifications) {
        try {
          const emailNotification: EmailNotification = {
            to: notification.recipient_email,
            subject: notification.subject,
            htmlContent: '', // Necesitaríamos regenerar el contenido
            textContent: '',
            type: notification.type,
            userId: notification.user_id,
            metadata: notification.metadata
          };

          // Marcar como reintento en metadata
          emailNotification.metadata = {
            ...emailNotification.metadata,
            retry_attempt: true,
            original_notification_id: notification.id,
            retry_at: new Date().toISOString()
          };

          const success = await this.sendEmail(emailNotification);
          
          if (success) {
            successful++;
            // Marcar la notificación original como reintentada exitosamente
            await EmailLogger.markAsRetried(notification.id, true);
          } else {
            failed++;
            await EmailLogger.markAsRetried(notification.id, false, 'Retry failed');
            errors.push(`Failed to retry notification ${notification.id}`);
          }

        } catch (retryError) {
          failed++;
          const errorMsg = `Error retrying notification ${notification.id}: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`;
          console.error(errorMsg);
          errors.push(errorMsg);
          await EmailLogger.markAsRetried(notification.id, false, errorMsg);
        }
      }

       (`🎯 Reintentos completados: ${successful} exitosos, ${failed} fallidos`);

      return {
        attempted: failedNotifications.length,
        successful,
        failed,
        errors
      };

    } catch (error) {
      console.error('❌ Error en retryFailedNotifications:', error);
      throw error;
    }
  }

  /**
   * Enviar email de prueba para verificar configuración
   */
  static async sendTestEmail(
    to: string,
    providerConfig?: any
  ): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const testNotification: EmailNotification = {
        to,
        subject: 'Test Email - OposI',
        htmlContent: `
          <h1>Email de Prueba</h1>
          <p>Este es un email de prueba para verificar la configuración del sistema de notificaciones.</p>
          <p>Enviado el: ${new Date().toLocaleString('es-ES')}</p>
        `,
        textContent: `
          Email de Prueba
          
          Este es un email de prueba para verificar la configuración del sistema de notificaciones.
          Enviado el: ${new Date().toLocaleString('es-ES')}
        `,
        type: 'other',
        metadata: {
          test_email: true,
          sent_at: new Date().toISOString()
        }
      };

      const success = await this.sendEmail(testNotification);

      return {
        success,
        message: success ? 'Email de prueba enviado exitosamente' : 'Fallo al enviar email de prueba',
        details: {
          to,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      console.error('Error enviando email de prueba:', error);
      return {
        success: false,
        message: 'Error enviando email de prueba',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error'
        }
      };
    }
  }

  /**
   * Validar configuración del proveedor de email
   */
  static async validateEmailProvider(): Promise<{
    isValid: boolean;
    provider: string;
    message: string;
  }> {
    try {
      // TODO: Implementar validación específica del proveedor
      // Ejemplo para Resend:
      /*
      if (!process.env.RESEND_API_KEY) {
        return {
          isValid: false,
          provider: 'Resend',
          message: 'RESEND_API_KEY no configurada'
        };
      }
      
      // Probar conexión con API
      const resend = new Resend(process.env.RESEND_API_KEY);
      await resend.domains.list();
      */

      // Por ahora, simulación
      return {
        isValid: true,
        provider: 'Simulado',
        message: 'Proveedor de email configurado correctamente'
      };

    } catch (error) {
      return {
        isValid: false,
        provider: 'Unknown',
        message: error instanceof Error ? error.message : 'Error validando proveedor'
      };
    }
  }
}
